#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星综合数据收集服务
实现完整的股票数据收集自动化流程 - 专业级实现
"""

import asyncio
import sqlite3
import aiosqlite
from datetime import datetime, timedelta, time
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path
import schedule
import threading

logger = logging.getLogger(__name__)

class ComprehensiveDataCollectionService:
    """瑶光星综合数据收集服务 - 完整专业实现"""
    
    def __init__(self):
        self.db_path = "backend/data/daily_stock_data.db"
        self.historical_db_path = "backend/data/stock_database.db"
        self.is_collecting = False
        self.is_realtime_collecting = False
        self.collection_stats = {
            "daily_collections": 0,
            "realtime_collections": 0,
            "last_daily_collection": None,
            "last_realtime_collection": None,
            "selected_stocks_today": [],
            "realtime_data_points": 0
        }
        
        # 市场交易时间配置
        self.market_open_time = time(9, 30)  # 9:30
        self.market_close_time = time(15, 0)  # 15:00
        self.evening_collection_time = time(18, 0)  # 18:00 晚上收集
        
        # 确保数据库目录存在
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        # 启动定时任务
        self._setup_scheduled_tasks()
        
        logger.info("🚀 瑶光星综合数据收集服务初始化完成 - 专业级实现")
    
    def _init_database(self):
        """初始化当日股票数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建当日股票数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_stock_data (
                        stock_code TEXT PRIMARY KEY,
                        stock_name TEXT,
                        current_price REAL,
                        open_price REAL,
                        high_price REAL,
                        low_price REAL,
                        volume INTEGER,
                        turnover_rate REAL,
                        pe_ratio REAL,
                        pb_ratio REAL,
                        market_cap REAL,
                        change_percent REAL,
                        change_amount REAL,
                        industry TEXT,
                        update_time TEXT,
                        data_source TEXT,
                        collection_date TEXT
                    )
                ''')
                
                # 创建分钟级数据表（实盘模式用）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS minute_stock_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code TEXT,
                        price REAL,
                        volume INTEGER,
                        timestamp TEXT,
                        data_source TEXT,
                        collection_date TEXT,
                        FOREIGN KEY (stock_code) REFERENCES daily_stock_data (stock_code)
                    )
                ''')
                
                # 创建开阳星选股记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS kaiyang_selections (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code TEXT,
                        selection_date TEXT,
                        selection_reason TEXT,
                        is_active INTEGER DEFAULT 1,
                        realtime_monitoring INTEGER DEFAULT 0
                    )
                ''')
                
                # 创建数据收集任务记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS collection_tasks (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        task_type TEXT,
                        task_date TEXT,
                        start_time TEXT,
                        end_time TEXT,
                        stocks_processed INTEGER,
                        success_count INTEGER,
                        failure_count INTEGER,
                        status TEXT,
                        details TEXT
                    )
                ''')
                
                conn.commit()
                logger.info("✅ 当日股票数据库初始化成功")
                
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            raise
    
    def _setup_scheduled_tasks(self):
        """设置定时任务"""
        try:
            # 每天晚上18:00执行全市场数据收集
            schedule.every().day.at("18:00").do(self._schedule_daily_collection)
            
            # 交易时间内每分钟执行实时数据收集
            schedule.every().minute.do(self._schedule_realtime_collection)
            
            # 启动调度器线程
            def run_scheduler():
                import time
                while True:
                    schedule.run_pending()
                    time.sleep(1)  # 使用同步sleep

            scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
            scheduler_thread.start()
            
            logger.info("✅ 定时任务设置完成")
            
        except Exception as e:
            logger.error(f"❌ 定时任务设置失败: {e}")
    
    def _schedule_daily_collection(self):
        """调度每日数据收集"""
        # 使用线程池执行异步任务
        import concurrent.futures
        import threading

        def run_async_task():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.execute_daily_market_collection())
                loop.close()
            except Exception as e:
                logger.error(f"定时任务执行失败: {e}")

        thread = threading.Thread(target=run_async_task, daemon=True)
        thread.start()

    def _schedule_realtime_collection(self):
        """调度实时数据收集"""
        current_time = datetime.now().time()
        if self.market_open_time <= current_time <= self.market_close_time:
            # 使用线程池执行异步任务
            import threading

            def run_async_task():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.execute_realtime_collection())
                    loop.close()
                except Exception as e:
                    logger.error(f"实时收集任务执行失败: {e}")

            thread = threading.Thread(target=run_async_task, daemon=True)
            thread.start()

    async def execute_daily_market_collection(self) -> Dict[str, Any]:
        """执行每日全市场数据收集 - 瑶光星核心功能"""
        if self.is_collecting:
            return {
                "success": False,
                "message": "数据收集已在进行中",
                "timestamp": datetime.now().isoformat()
            }
        
        self.is_collecting = True
        start_time = datetime.now()
        
        try:
            logger.info("🌟 瑶光星开始每日全市场数据收集")
            
            # 记录任务开始
            task_id = await self._record_collection_task("daily_market", "started")
            
            # 获取全市场A股列表
            stock_list = await self._get_all_a_stocks()
            
            if not stock_list:
                raise Exception("获取A股列表失败")
            
            logger.info(f"📊 准备收集 {len(stock_list)} 只A股的当日数据")
            
            # 导入数据管理服务
            from .data_management_service import data_management_service
            
            # 批量收集实时数据
            collection_result = await data_management_service.batch_get_realtime_data(
                stock_codes=stock_list,
                batch_size=50,
                delay_between_batches=2
            )
            
            # 保存到当日数据库
            saved_count = 0
            failed_count = 0
            
            if collection_result.get("success") and collection_result.get("results"):
                for stock_code, stock_data in collection_result["results"].items():
                    if stock_data and stock_data.get("success"):
                        success = await self._save_daily_stock_data(stock_code, stock_data["data"])
                        if success:
                            saved_count += 1
                        else:
                            failed_count += 1
                    else:
                        failed_count += 1
            
            # 更新统计信息
            self.collection_stats["daily_collections"] += 1
            self.collection_stats["last_daily_collection"] = datetime.now().isoformat()
            
            # 记录任务完成
            await self._record_collection_task(
                "daily_market", 
                "completed",
                task_id,
                len(stock_list),
                saved_count,
                failed_count
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            result = {
                "success": True,
                "collection_type": "daily_market",
                "total_stocks": len(stock_list),
                "saved_count": saved_count,
                "failed_count": failed_count,
                "success_rate": saved_count / len(stock_list) if stock_list else 0,
                "duration_seconds": duration,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "data_source": "瑶光星-每日收集"
            }
            
            logger.info(f"✅ 瑶光星每日数据收集完成: {saved_count}/{len(stock_list)} 成功")
            return result
            
        except Exception as e:
            logger.error(f"❌ 瑶光星每日数据收集失败: {e}")
            
            # 记录任务失败
            await self._record_collection_task("daily_market", "failed", details=str(e))
            
            return {
                "success": False,
                "error": str(e),
                "collection_type": "daily_market",
                "timestamp": datetime.now().isoformat()
            }
        finally:
            self.is_collecting = False

    async def execute_realtime_collection(self) -> Dict[str, Any]:
        """执行实时数据收集 - 针对开阳星选中的股票"""
        if self.is_realtime_collecting:
            return {"success": False, "message": "实时收集已在进行中"}
        
        self.is_realtime_collecting = True
        
        try:
            # 获取开阳星今日选中的股票
            selected_stocks = await self._get_kaiyang_selected_stocks()
            
            if not selected_stocks:
                return {
                    "success": True,
                    "message": "今日无开阳星选中股票",
                    "timestamp": datetime.now().isoformat()
                }
            
            logger.info(f"📈 瑶光星开始实时收集 {len(selected_stocks)} 只选中股票数据")
            
            # 导入数据管理服务
            from .data_management_service import data_management_service
            
            # 收集实时数据
            realtime_data = await data_management_service.batch_get_realtime_data(
                stock_codes=selected_stocks,
                batch_size=20,
                delay_between_batches=0.5
            )
            
            # 保存分钟级数据
            saved_count = 0
            
            if realtime_data.get("success") and realtime_data.get("results"):
                for stock_code, stock_data in realtime_data["results"].items():
                    if stock_data and stock_data.get("success"):
                        data = stock_data["data"]
                        success = await self._save_minute_data(
                            stock_code,
                            data.get("current_price", 0.0),
                            data.get("volume", 0)
                        )
                        if success:
                            saved_count += 1
            
            # 更新统计信息
            self.collection_stats["realtime_collections"] += 1
            self.collection_stats["last_realtime_collection"] = datetime.now().isoformat()
            self.collection_stats["realtime_data_points"] += saved_count
            
            return {
                "success": True,
                "collection_type": "realtime",
                "selected_stocks": len(selected_stocks),
                "data_points_saved": saved_count,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 实时数据收集失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "collection_type": "realtime",
                "timestamp": datetime.now().isoformat()
            }
        finally:
            self.is_realtime_collecting = False

    async def _get_all_a_stocks(self) -> List[str]:
        """获取全市场A股列表"""
        try:
            # 从历史数据库获取股票列表
            async with aiosqlite.connect(self.historical_db_path) as conn:
                conn.row_factory = aiosqlite.Row

                # 尝试从不同表获取股票代码
                tables = ['stock_info', 'stocks', 'stock_basic']

                for table in tables:
                    try:
                        cursor = await conn.execute(f'SELECT DISTINCT stock_code FROM {table} LIMIT 5000')
                        rows = await cursor.fetchall()

                        if rows:
                            stock_codes = [row['stock_code'] for row in rows]
                            logger.info(f"从表 {table} 获取到 {len(stock_codes)} 只股票")
                            return stock_codes
                    except Exception:
                        continue

                # 如果数据库没有数据，使用备用股票列表
                logger.warning("历史数据库无可用数据，使用备用股票列表")
                return [
                    "000001", "000002", "000858", "002415", "600000", "600036", "600519", "600887",
                    "000858", "002594", "300059", "300750", "688981", "688599", "300760", "002475"
                ]

        except Exception as e:
            logger.error(f"获取A股列表失败: {e}")
            return []

    async def _get_kaiyang_selected_stocks(self) -> List[str]:
        """获取开阳星今日选中的股票"""
        try:
            today = datetime.now().date().isoformat()

            async with aiosqlite.connect(self.db_path) as conn:
                conn.row_factory = aiosqlite.Row
                cursor = await conn.execute('''
                    SELECT DISTINCT stock_code FROM kaiyang_selections
                    WHERE selection_date = ? AND is_active = 1
                ''', (today,))

                rows = await cursor.fetchall()
                selected_stocks = [row['stock_code'] for row in rows]

                # 更新统计信息
                self.collection_stats["selected_stocks_today"] = selected_stocks

                return selected_stocks

        except Exception as e:
            logger.error(f"获取开阳星选中股票失败: {e}")
            return []

    async def _save_daily_stock_data(self, stock_code: str, stock_data: Dict[str, Any]) -> bool:
        """保存当日股票数据"""
        try:
            today = datetime.now().date().isoformat()

            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('''
                    INSERT OR REPLACE INTO daily_stock_data
                    (stock_code, stock_name, current_price, open_price, high_price, low_price,
                     volume, turnover_rate, pe_ratio, pb_ratio, market_cap, change_percent,
                     change_amount, industry, update_time, data_source, collection_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    stock_code,
                    stock_data.get('stock_name', ''),
                    stock_data.get('current_price', 0.0),
                    stock_data.get('open_price', 0.0),
                    stock_data.get('high_price', 0.0),
                    stock_data.get('low_price', 0.0),
                    stock_data.get('volume', 0),
                    stock_data.get('turnover_rate', 0.0),
                    stock_data.get('pe_ratio', 0.0),
                    stock_data.get('pb_ratio', 0.0),
                    stock_data.get('market_cap', 0.0),
                    stock_data.get('change_percent', 0.0),
                    stock_data.get('change_amount', 0.0),
                    stock_data.get('industry', ''),
                    datetime.now().isoformat(),
                    "瑶光星-每日收集",
                    today
                ))
                await conn.commit()

            return True

        except Exception as e:
            logger.error(f"保存当日股票数据失败 {stock_code}: {e}")
            return False

    async def _save_minute_data(self, stock_code: str, price: float, volume: int) -> bool:
        """保存分钟级数据"""
        try:
            today = datetime.now().date().isoformat()

            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('''
                    INSERT INTO minute_stock_data
                    (stock_code, price, volume, timestamp, data_source, collection_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    stock_code,
                    price,
                    volume,
                    datetime.now().isoformat(),
                    "瑶光星-实时收集",
                    today
                ))
                await conn.commit()

            return True

        except Exception as e:
            logger.error(f"保存分钟级数据失败 {stock_code}: {e}")
            return False

    async def _record_collection_task(self, task_type: str, status: str, task_id: int = None,
                                    stocks_processed: int = 0, success_count: int = 0,
                                    failure_count: int = 0, details: str = "") -> int:
        """记录数据收集任务"""
        try:
            today = datetime.now().date().isoformat()
            current_time = datetime.now().isoformat()

            async with aiosqlite.connect(self.db_path) as conn:
                if task_id is None:
                    # 创建新任务记录
                    cursor = await conn.execute('''
                        INSERT INTO collection_tasks
                        (task_type, task_date, start_time, status, details)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (task_type, today, current_time, status, details))

                    task_id = cursor.lastrowid
                else:
                    # 更新现有任务记录
                    await conn.execute('''
                        UPDATE collection_tasks
                        SET end_time = ?, stocks_processed = ?, success_count = ?,
                            failure_count = ?, status = ?, details = ?
                        WHERE id = ?
                    ''', (current_time, stocks_processed, success_count, failure_count, status, details, task_id))

                await conn.commit()
                return task_id

        except Exception as e:
            logger.error(f"记录收集任务失败: {e}")
            return 0

    async def register_kaiyang_selection(self, stock_code: str, selection_reason: str) -> bool:
        """注册开阳星选股结果"""
        try:
            today = datetime.now().date().isoformat()

            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('''
                    INSERT OR REPLACE INTO kaiyang_selections
                    (stock_code, selection_date, selection_reason, is_active, realtime_monitoring)
                    VALUES (?, ?, ?, 1, 1)
                ''', (stock_code, today, selection_reason))

                await conn.commit()

            logger.info(f"✅ 注册开阳星选股: {stock_code} - {selection_reason}")
            return True

        except Exception as e:
            logger.error(f"注册开阳星选股失败 {stock_code}: {e}")
            return False

    async def get_stock_data_for_other_stars(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """为其他星提供股票数据"""
        try:
            today = datetime.now().date().isoformat()

            async with aiosqlite.connect(self.db_path) as conn:
                conn.row_factory = aiosqlite.Row

                # 优先获取当日数据
                cursor = await conn.execute('''
                    SELECT * FROM daily_stock_data
                    WHERE stock_code = ?
                ''', (stock_code,))

                row = await cursor.fetchone()

                if row:
                    return dict(row)
                else:
                    # 如果当日数据不存在，从历史库获取基础信息
                    return await self._get_from_historical_db(stock_code)

        except Exception as e:
            logger.error(f"获取股票数据失败 {stock_code}: {e}")
            return None

    async def get_collection_stats(self) -> Dict[str, Any]:
        """获取收集统计信息"""
        try:
            today = datetime.now().date().isoformat()

            async with aiosqlite.connect(self.db_path) as conn:
                # 获取今日数据统计
                cursor = await conn.execute('''
                    SELECT COUNT(*) as daily_count FROM daily_stock_data
                ''')
                daily_count = (await cursor.fetchone())[0]

                # 获取今日分钟级数据统计
                cursor = await conn.execute('''
                    SELECT COUNT(*) as minute_count FROM minute_stock_data
                ''')
                minute_count = (await cursor.fetchone())[0]

                # 获取开阳星选股统计
                cursor = await conn.execute('''
                    SELECT COUNT(*) as selected_count FROM kaiyang_selections
                    WHERE selection_date = ? AND is_active = 1
                ''', (today,))
                selected_count = (await cursor.fetchone())[0]

            return {
                "collection_date": today,
                "daily_stocks_collected": daily_count,
                "minute_data_points": minute_count,
                "kaiyang_selected_stocks": selected_count,
                "system_stats": self.collection_stats,
                "is_collecting": self.is_collecting,
                "is_realtime_collecting": self.is_realtime_collecting
            }

        except Exception as e:
            logger.error(f"获取收集统计失败: {e}")
            return {}

    async def _get_from_historical_db(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """从历史数据库获取股票基础信息"""
        try:
            async with aiosqlite.connect(self.historical_db_path) as conn:
                conn.row_factory = aiosqlite.Row

                tables = ['stock_info', 'stocks', 'stock_basic']

                for table in tables:
                    try:
                        cursor = await conn.execute(f'SELECT * FROM {table} WHERE stock_code = ? LIMIT 1', (stock_code,))
                        row = await cursor.fetchone()

                        if row:
                            data = dict(row)
                            return {
                                'stock_code': stock_code,
                                'stock_name': data.get('stock_name', data.get('name', '')),
                                'current_price': 0.0,
                                'industry': data.get('industry', ''),
                                'market_cap': data.get('market_cap', 0.0),
                                'data_source': 'historical_db',
                                'update_time': datetime.now().isoformat()
                            }
                    except Exception:
                        continue

                return None

        except Exception as e:
            logger.error(f"从历史数据库获取数据失败 {stock_code}: {e}")
            return None


# 创建全局实例
comprehensive_data_collection_service = ComprehensiveDataCollectionService()
