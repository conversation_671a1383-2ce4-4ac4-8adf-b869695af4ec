#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星统一系统
整合所有瑶光星功能，消除重复代码，提供真实完整的自动化系统
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd

logger = logging.getLogger(__name__)

class UnifiedYaoguangSystem:
    """瑶光星统一系统 - 唯一的瑶光星核心"""
    
    def __init__(self):
        self.system_name = "瑶光星统一量化研究系统"
        self.version = "3.0.0"
        self.system_id = f"yaoguang_{datetime.now().strftime('%Y%m%d')}"
        
        # 系统状态
        self.is_active = False
        self.current_mode = None  # "learning" 或 "live_trading"
        self.current_session = None
        
        # 核心组件
        self.core_systems = None
        self.data_persistence = None
        self.automation_engine = None
        
        # 历史记录
        self.session_history = []
        self.performance_metrics = {
            "total_sessions": 0,
            "successful_sessions": 0,
            "learning_sessions": 0,
            "trading_sessions": 0,
            "total_stocks_analyzed": 0,
            "total_strategies_tested": 0,
            "system_uptime": 0
        }
        
        # 配置
        self.system_config = {
            "learning_mode": {
                "enabled": True,
                "stocks_per_session": 5,
                "data_years": 10,
                "strategy_testing_enabled": True,
                "four_stars_debate_enabled": True
            },
            "live_trading_mode": {
                "enabled": False,  # 默认关闭实盘交易
                "risk_management_enabled": True,
                "max_position_size": 0.1,
                "stop_loss_threshold": 0.05
            },
            "data_sources": {
                "local_database_enabled": True,
                "real_time_data_enabled": True,
                "backup_sources_enabled": True
            }
        }
        
        logger.info(f"🌟 {self.system_name} v{self.version} 初始化完成")
    
    async def initialize_system(self) -> Dict[str, Any]:
        """初始化系统所有组件"""
        try:
            logger.info("🔧 初始化瑶光星统一系统...")
            
            # 1. 初始化四大核心系统
            await self._initialize_core_systems()
            
            # 2. 初始化数据持久化
            await self._initialize_data_persistence()
            
            # 3. 初始化自动化引擎
            await self._initialize_automation_engine()
            
            # 4. 验证系统完整性
            system_health = await self._verify_system_health()
            
            if system_health["overall_health"]:
                self.is_active = True
                logger.info("✅ 瑶光星统一系统初始化成功")
                
                return {
                    "success": True,
                    "message": "瑶光星统一系统初始化成功",
                    "system_id": self.system_id,
                    "version": self.version,
                    "health_status": system_health,
                    "available_modes": ["learning", "live_trading"],
                    "timestamp": datetime.now().isoformat()
                }
            else:
                raise Exception(f"系统健康检查失败: {system_health}")
                
        except Exception as e:
            logger.error(f"瑶光星统一系统初始化失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def start_learning_session(self, session_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """启动学习模式会话"""
        try:
            if self.is_active and self.current_session:
                return {
                    "success": False,
                    "message": "已有活跃会话正在进行",
                    "current_session": self.current_session["session_id"]
                }
            
            # 合并配置
            config = self.system_config["learning_mode"].copy()
            if session_config:
                config.update(session_config)
            
            # 创建学习会话
            session = {
                "session_id": f"learning_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "mode": "learning",
                "start_time": datetime.now().isoformat(),
                "config": config,
                "status": "running",
                "progress": {
                    "current_step": "初始化",
                    "completed_steps": [],
                    "total_stocks": config.get("stocks_per_session", 5),
                    "processed_stocks": 0,
                    "current_stock": None
                },
                "results": {
                    "selected_stocks": [],
                    "strategy_results": {},
                    "learning_insights": [],
                    "performance_summary": {}
                }
            }
            
            self.current_session = session
            self.current_mode = "learning"
            
            # 异步执行学习流程
            asyncio.create_task(self._execute_learning_flow(session))
            
            logger.info(f"🎓 启动学习会话: {session['session_id']}")
            
            return {
                "success": True,
                "message": "学习会话启动成功",
                "session_id": session["session_id"],
                "mode": "learning",
                "config": config,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"启动学习会话失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def start_live_trading_session(self, session_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """启动实盘交易会话"""
        try:
            if not self.system_config["live_trading_mode"]["enabled"]:
                return {
                    "success": False,
                    "message": "实盘交易模式未启用",
                    "recommendation": "请先在系统配置中启用实盘交易模式"
                }
            
            if self.is_active and self.current_session:
                return {
                    "success": False,
                    "message": "已有活跃会话正在进行",
                    "current_session": self.current_session["session_id"]
                }
            
            # 合并配置
            config = self.system_config["live_trading_mode"].copy()
            if session_config:
                config.update(session_config)
            
            # 创建实盘交易会话
            session = {
                "session_id": f"trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "mode": "live_trading",
                "start_time": datetime.now().isoformat(),
                "config": config,
                "status": "running",
                "progress": {
                    "current_step": "初始化",
                    "completed_steps": [],
                    "total_positions": 0,
                    "active_positions": 0,
                    "current_action": None
                },
                "results": {
                    "trading_decisions": [],
                    "position_changes": [],
                    "performance_metrics": {},
                    "risk_metrics": {}
                }
            }
            
            self.current_session = session
            self.current_mode = "live_trading"
            
            # 异步执行实盘交易流程
            asyncio.create_task(self._execute_live_trading_flow(session))
            
            logger.info(f"💰 启动实盘交易会话: {session['session_id']}")
            
            return {
                "success": True,
                "message": "实盘交易会话启动成功",
                "session_id": session["session_id"],
                "mode": "live_trading",
                "config": config,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"启动实盘交易会话失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_learning_status(self) -> Dict[str, Any]:
        """获取学习状态"""
        try:
            current_session = self.current_session
            if not current_session:
                return {
                    "session_active": False,
                    "message": "没有活跃的学习会话"
                }

            # 获取会话状态
            results = current_session.get("results", {})
            progress = current_session.get("progress", {})

            # 确定当前阶段
            current_phase = progress.get("current_step", "初始化")

            # 确定参与的角色
            participating_roles = []
            if results.get("selected_stocks"):
                participating_roles.append("开阳星")
            if results.get("market_info"):
                participating_roles.append("天枢星")
            if results.get("data_collection"):
                participating_roles.append("瑶光星")
            if results.get("risk_analysis"):
                participating_roles.append("天玑星")
            if results.get("technical_analysis"):
                participating_roles.append("天璇星")
            if results.get("strategy_testing"):
                participating_roles.append("天权星")
            if results.get("trading_execution"):
                participating_roles.append("玉衡星")

            # 获取选中的股票
            selected_stocks = results.get("selected_stocks", [])

            session_status = {
                "session_active": True,
                "session_id": current_session.get("session_id"),
                "start_time": current_session.get("start_time"),
                "config": current_session.get("config", {}),
                "status": current_session.get("status", "unknown"),
                "current_phase": current_phase,
                "participating_roles": participating_roles,
                "selected_stocks": selected_stocks,
                "processed_stocks": len(current_session.get("processed_stocks", [])),
                "current_stock": current_session.get("current_stock"),
                "learning_progress": current_session.get("learning_progress", 0)
            }

            # 获取时间控制引擎状态
            if hasattr(self, 'automation_engine') and self.automation_engine and self.automation_engine.get("time_control"):
                time_control = self.automation_engine["time_control"]
                try:
                    time_status = time_control.get_session_status(current_session.get("session_id"))
                    session_status.update(time_status)
                except Exception as e:
                    logger.debug(f"获取时间控制状态失败: {e}")

            return session_status

        except Exception as e:
            logger.error(f"获取学习状态失败: {e}")
            return {
                "session_active": False,
                "error": str(e)
            }

    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 获取核心系统状态
            core_status = {}
            if self.core_systems:
                if hasattr(self.core_systems, 'get_integration_status'):
                    if asyncio.iscoroutinefunction(self.core_systems.get_integration_status):
                        core_status = await self.core_systems.get_integration_status()
                    else:
                        core_status = self.core_systems.get_integration_status()
                else:
                    core_status = {"overall_health": True}
            
            # 获取数据持久化状态
            persistence_status = {}
            if self.data_persistence:
                persistence_status = self.data_persistence.get_system_status()
            
            # 计算系统运行时间
            if self.performance_metrics.get("system_start_time"):
                start_time = datetime.fromisoformat(self.performance_metrics["system_start_time"])
                uptime_hours = (datetime.now() - start_time).total_seconds() / 3600
                self.performance_metrics["system_uptime"] = uptime_hours
            
            return {
                "success": True,
                "system_info": {
                    "name": self.system_name,
                    "version": self.version,
                    "system_id": self.system_id,
                    "is_active": self.is_active,
                    "current_mode": self.current_mode,
                    "current_session": self.current_session["session_id"] if self.current_session else None
                },
                "core_systems_status": core_status,
                "data_persistence_status": persistence_status,
                "performance_metrics": self.performance_metrics,
                "system_config": self.system_config,
                "session_history_count": len(self.session_history),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def stop_current_session(self) -> Dict[str, Any]:
        """停止当前会话"""
        try:
            if not self.current_session:
                return {
                    "success": False,
                    "message": "当前没有活跃会话"
                }
            
            session_id = self.current_session["session_id"]
            session_mode = self.current_session["mode"]
            
            # 标记会话为停止状态
            self.current_session["status"] = "stopped"
            self.current_session["end_time"] = datetime.now().isoformat()
            self.current_session["progress"]["current_step"] = "已停止"
            
            # 生成学习报告（仅对学习模式）
            if session_mode == "learning":
                try:
                    from roles.yaoguang_star.services.learning_report_generator import learning_report_generator
                    report_result = await learning_report_generator.generate_comprehensive_report(self.current_session)
                    if report_result.get("success"):
                        logger.info("📋 学习报告生成成功")
                        # 将报告添加到会话数据中
                        self.current_session["learning_report"] = report_result["report"]

                        # 打印学习报告摘要
                        report = report_result["report"]
                        logger.info("=" * 60)
                        logger.info("🎓 瑶光星学习报告摘要")
                        logger.info("=" * 60)
                        logger.info(f"📊 总体评分: {report.get('overall_score', 0)}/100")
                        logger.info(f"💰 交易盈亏: {report.get('trading_summary', {}).get('total_pnl', 0):.2f}元")
                        logger.info(f"📈 收益率: {report.get('trading_summary', {}).get('return_rate', 0):.4f}%")
                        logger.info(f"🔬 生成因子: {report.get('factor_achievements', {}).get('generated_factors', 0)}个")
                        logger.info("=" * 60)
                    else:
                        logger.warning(f"学习报告生成失败: {report_result.get('error')}")
                except Exception as e:
                    logger.warning(f"学习报告生成异常: {e}")

            # 保存到历史记录
            self.session_history.append(self.current_session.copy())

            # 更新性能指标
            self.performance_metrics["total_sessions"] += 1
            if session_mode == "learning":
                self.performance_metrics["learning_sessions"] += 1
            elif session_mode == "live_trading":
                self.performance_metrics["trading_sessions"] += 1
            
            # 清理当前会话
            self.current_session = None
            self.current_mode = None
            
            logger.info(f"🛑 停止会话: {session_id}")
            
            return {
                "success": True,
                "message": f"会话已停止: {session_id}",
                "stopped_session": session_id,
                "session_mode": session_mode,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"停止会话失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """获取学习会话总结"""
        try:
            # 从历史记录中查找会话
            session_data = None
            for session in self.session_history:
                if session.get("session_id") == session_id:
                    session_data = session
                    break

            if not session_data:
                # 检查当前会话
                if self.current_session and self.current_session.get("session_id") == session_id:
                    session_data = self.current_session

            if session_data:
                # 计算会话统计
                start_time = session_data.get("start_time")
                end_time = session_data.get("end_time", datetime.now().isoformat())

                # 计算持续时间
                if start_time:
                    try:
                        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                        duration = str(end_dt - start_dt)
                    except:
                        duration = "未知"
                else:
                    duration = "未知"

                results = session_data.get("results", {})

                return {
                    "session_id": session_id,
                    "duration": duration,
                    "processed_stocks": len(results.get("selected_stocks", [])),
                    "generated_factors": len(results.get("factor_research", {}).get("generated_factors", [])),
                    "executed_trades": len(results.get("trading_execution", {}).get("trades", [])),
                    "learning_records": len(results.get("learning_optimization", {}).get("records", [])),
                    "debate_sessions": len(results.get("debate_results", {}).get("sessions", [])),
                    "status": session_data.get("status", "unknown"),
                    "start_time": start_time,
                    "end_time": end_time,
                    "mode": session_data.get("mode", "unknown")
                }

            # 如果没有找到会话数据
            return {
                "session_id": session_id,
                "duration": "未知",
                "processed_stocks": 0,
                "generated_factors": 0,
                "executed_trades": 0,
                "learning_records": 0,
                "debate_sessions": 0,
                "status": "not_found",
                "message": "会话数据不存在"
            }

        except Exception as e:
            logger.error(f"获取会话总结失败: {e}")
            return {
                "session_id": session_id,
                "error": str(e),
                "status": "error"
            }

    async def _initialize_core_systems(self):
        """初始化四大核心系统"""
        try:
            from .core_systems_integration import yaoguang_core_systems
            self.core_systems = yaoguang_core_systems

            # 验证核心系统
            if hasattr(self.core_systems, 'get_integration_status'):
                if asyncio.iscoroutinefunction(self.core_systems.get_integration_status):
                    status = await self.core_systems.get_integration_status()
                else:
                    status = self.core_systems.get_integration_status()
            else:
                status = {"overall_health": True}  # 默认健康状态

            if not status.get("overall_health"):
                raise Exception("四大核心系统集成失败")

            logger.info("✅ 四大核心系统初始化成功")

        except Exception as e:
            logger.error(f"四大核心系统初始化失败: {e}")
            raise

    async def _initialize_data_persistence(self):
        """初始化数据持久化"""
        try:
            from .data_persistence import yaoguang_persistence
            self.data_persistence = yaoguang_persistence

            # 验证数据持久化
            status = self.data_persistence.get_system_status()
            if not status.get("database_connected"):
                raise Exception("数据持久化系统连接失败")

            logger.info("✅ 数据持久化系统初始化成功")

        except Exception as e:
            logger.error(f"数据持久化系统初始化失败: {e}")
            raise

    async def _initialize_automation_engine(self):
        """初始化自动化引擎"""
        try:
            # 创建统一的自动化引擎
            self.automation_engine = {
                "kaiyang_selection": None,
                "tianquan_strategies": None,
                "four_stars_debate": None,
                "data_collection": None,
                "learning_optimization": None
            }

            # 初始化开阳星选股
            try:
                from roles.kaiyang_star.services.stock_selection_service import stock_selection_service
                self.automation_engine["kaiyang_selection"] = stock_selection_service
                logger.info("✅ 开阳星选股服务集成成功")
            except ImportError as e:
                logger.warning(f"开阳星选股服务集成失败: {e}")

            # 初始化天权星战法
            try:
                from roles.tianquan_star.services.advanced_strategy_adjustment_system import advanced_strategy_adjustment_system
                self.automation_engine["tianquan_strategies"] = advanced_strategy_adjustment_system
                logger.info("✅ 天权星战法服务集成成功")
            except ImportError as e:
                logger.warning(f"天权星战法服务集成失败: {e}")

            # 初始化四星辩论系统
            try:
                from roles.yaoguang_star.services.real_debate_system import RealDebateSystem
                self.automation_engine["four_stars_debate"] = RealDebateSystem()
                logger.info("✅ 四星辩论系统集成成功")
            except ImportError as e:
                logger.warning(f"四星辩论系统集成失败: {e}")

            # 初始化数据收集
            try:
                from ..services.ten_year_data_collector import ten_year_data_collector
                self.automation_engine["data_collection"] = ten_year_data_collector
                logger.info("✅ 数据收集服务集成成功")
            except ImportError as e:
                logger.warning(f"数据收集服务集成失败: {e}")

            # 初始化时间控制引擎
            try:
                from ..core.time_control_engine import time_control_engine
                self.automation_engine["time_control"] = time_control_engine
                logger.info("✅ 时间控制引擎集成成功")
            except ImportError as e:
                logger.warning(f"时间控制引擎集成失败: {e}")

            logger.info("✅ 自动化引擎初始化完成")

        except Exception as e:
            logger.error(f"自动化引擎初始化失败: {e}")
            raise

    async def _verify_system_health(self) -> Dict[str, Any]:
        """验证系统健康状态"""
        try:
            health_status = {
                "core_systems": False,
                "data_persistence": False,
                "automation_engine": False,
                "overall_health": False
            }

            # 检查核心系统
            if self.core_systems:
                if hasattr(self.core_systems, 'get_integration_status'):
                    if asyncio.iscoroutinefunction(self.core_systems.get_integration_status):
                        core_status = await self.core_systems.get_integration_status()
                    else:
                        core_status = self.core_systems.get_integration_status()
                else:
                    core_status = {"overall_health": True}
                health_status["core_systems"] = core_status.get("overall_health", False)

            # 检查数据持久化
            if self.data_persistence:
                persistence_status = self.data_persistence.get_system_status()
                health_status["data_persistence"] = persistence_status.get("database_connected", False)

            # 检查自动化引擎
            if self.automation_engine:
                available_services = sum(1 for service in self.automation_engine.values() if service is not None)
                health_status["automation_engine"] = available_services >= 2  # 至少2个服务可用

            # 计算总体健康状态
            health_status["overall_health"] = (
                health_status["core_systems"] and
                health_status["data_persistence"] and
                health_status["automation_engine"]
            )

            return health_status

        except Exception as e:
            logger.error(f"系统健康检查失败: {e}")
            return {
                "core_systems": False,
                "data_persistence": False,
                "automation_engine": False,
                "overall_health": False,
                "error": str(e)
            }

    async def _execute_learning_flow(self, session: Dict[str, Any]):
        """执行真实的学习流程"""
        try:
            session_id = session["session_id"]
            logger.info(f"🎓 执行真实学习流程: {session_id}")

            # 启动时间控制引擎
            if self.automation_engine.get("time_control"):
                time_control = self.automation_engine["time_control"]
                await time_control.start_learning_session(session_id, {
                    "session_type": "learning",
                    "estimated_duration": 1800,  # 30分钟
                    "max_duration": 3600  # 最大1小时
                })
                logger.info("⏰ 时间控制引擎已启动")

            # 步骤1：开阳星真实选股
            session["progress"]["current_step"] = "开阳星选股"
            selected_stocks = await self._real_kaiyang_stock_selection(session)
            session["results"]["selected_stocks"] = selected_stocks

            # 步骤2：天枢星真实市场信息收集
            session["progress"]["current_step"] = "天枢星市场信息收集"
            market_info_results = await self._real_tianshu_market_info_collection(session, selected_stocks)
            session["results"]["market_info"] = market_info_results

            # 步骤3：瑶光星真实数据收集
            session["progress"]["current_step"] = "瑶光星数据收集"
            data_collection_results = await self._real_data_collection(session, selected_stocks)
            session["results"]["data_collection"] = data_collection_results
            session["progress"]["processed_stocks"] = len(data_collection_results.get("successful_stocks", []))

            # 步骤4：天玑星真实风险分析
            session["progress"]["current_step"] = "天玑星风险分析"
            risk_analysis_results = await self._real_tianji_risk_analysis(session, data_collection_results)
            session["results"]["risk_analysis"] = risk_analysis_results

            # 步骤5：天璇星真实技术分析
            session["progress"]["current_step"] = "天璇星技术分析"
            technical_analysis_results = await self._real_tianxuan_technical_analysis(session, data_collection_results)
            session["results"]["technical_analysis"] = technical_analysis_results

            # 步骤6：天权星真实战法测试
            session["progress"]["current_step"] = "天权星战法测试"
            strategy_results = await self._real_tianquan_strategy_testing(session, data_collection_results)
            session["results"]["strategy_testing"] = strategy_results

            # 步骤7：四星真实辩论
            session["progress"]["current_step"] = "四星深度辩论"
            debate_results = await self._real_four_stars_debate(session, strategy_results)
            session["results"]["debate_results"] = debate_results

            # 步骤8：玉衡星真实交易执行（学习模式）
            session["progress"]["current_step"] = "玉衡星交易执行"
            trading_results = await self._real_yuheng_learning_trading(session, debate_results)
            session["results"]["trading_execution"] = trading_results

            # 步骤9：瑶光星真实学习优化
            session["progress"]["current_step"] = "学习优化"
            learning_results = await self._real_learning_optimization(session, debate_results)
            session["results"]["learning_optimization"] = learning_results

            # 步骤10：RD-Agent真实因子研究
            session["progress"]["current_step"] = "RD-Agent因子研究"
            factor_results = await self._real_rd_agent_research(session, learning_results)
            session["results"]["factor_research"] = factor_results

            # 完成学习会话
            session["status"] = "completed"
            session["end_time"] = datetime.now().isoformat()
            session["progress"]["current_step"] = "已完成"
            session["progress"]["processed_stocks"] = len(selected_stocks)

            # 更新性能指标
            self.performance_metrics["successful_sessions"] += 1
            self.performance_metrics["total_stocks_analyzed"] += len(selected_stocks)
            self.performance_metrics["learning_sessions"] += 1

            # 保存到历史记录
            self.session_history.append(session.copy())

            # 延迟清理当前会话，让监控系统能够看到完成状态
            import asyncio
            async def delayed_cleanup():
                await asyncio.sleep(30)  # 30秒后清理
                if self.current_session and self.current_session.get("session_id") == session_id:
                    self.current_session = None
                    self.current_mode = None
                    logger.info(f"🧹 延迟清理会话: {session_id}")

            # 启动延迟清理任务
            asyncio.create_task(delayed_cleanup())

            logger.info(f"✅ 真实学习流程完成: {session_id}")

        except Exception as e:
            logger.error(f"真实学习流程执行失败: {e}")
            session["status"] = "failed"
            session["error"] = str(e)
            session["end_time"] = datetime.now().isoformat()

            # 保存失败的会话
            self.session_history.append(session.copy())
            self.current_session = None
            self.current_mode = None

    async def _execute_live_trading_flow(self, session: Dict[str, Any]):
        """执行真实的实盘交易流程"""
        try:
            session_id = session["session_id"]
            logger.info(f"💰 执行真实实盘交易流程: {session_id}")

            # 步骤1：天枢星真实市场分析
            session["progress"]["current_step"] = "天枢星市场分析"
            market_analysis = await self._real_tianshu_market_analysis(session)
            session["results"]["market_analysis"] = market_analysis

            # 步骤2：开阳星真实选股
            session["progress"]["current_step"] = "开阳星实盘选股"
            trading_stocks = await self._real_kaiyang_trading_selection(session, market_analysis)
            session["results"]["trading_stocks"] = trading_stocks

            # 步骤3：天权星真实战法匹配
            session["progress"]["current_step"] = "天权星战法匹配"
            strategy_matching = await self._real_tianquan_strategy_matching(session, trading_stocks)
            session["results"]["strategy_matching"] = strategy_matching

            # 步骤4：天玑星真实风险评估
            session["progress"]["current_step"] = "天玑星风险评估"
            risk_assessment = await self._real_tianji_risk_assessment(session, strategy_matching)
            session["results"]["risk_assessment"] = risk_assessment

            # 步骤5：玉衡星真实交易执行
            session["progress"]["current_step"] = "玉衡星交易执行"
            execution_results = await self._real_yuheng_trade_execution(session, risk_assessment)
            session["results"]["execution_results"] = execution_results

            # 步骤6：持续监控
            session["progress"]["current_step"] = "持续监控"
            monitoring_setup = await self._real_continuous_monitoring(session, execution_results)
            session["results"]["monitoring_setup"] = monitoring_setup

            # 完成交易会话
            session["status"] = "completed"
            session["end_time"] = datetime.now().isoformat()
            session["progress"]["current_step"] = "已完成"

            # 更新性能指标
            self.performance_metrics["successful_sessions"] += 1
            self.performance_metrics["trading_sessions"] += 1

            # 保存到历史记录
            self.session_history.append(session.copy())

            # 清理当前会话
            self.current_session = None
            self.current_mode = None

            logger.info(f"✅ 真实实盘交易流程完成: {session_id}")

        except Exception as e:
            logger.error(f"真实实盘交易流程执行失败: {e}")
            session["status"] = "failed"
            session["error"] = str(e)
            session["end_time"] = datetime.now().isoformat()

            # 保存失败的会话
            self.session_history.append(session.copy())
            self.current_session = None
            self.current_mode = None

    async def _real_tianshu_market_analysis(self, session: Dict[str, Any]) -> Dict[str, Any]:
        """天枢星真实市场分析"""
        try:
            # 导入天枢星真实服务
            from roles.tianshu_star.services.market_sentiment_analyzer import market_sentiment_analyzer
            from roles.tianshu_star.services.real_time_data_service import real_time_data_service

            # 执行真实的市场情绪分析
            sentiment_result = await market_sentiment_analyzer.analyze_market_sentiment()

            # 获取真实的实时市场数据
            market_data = await real_time_data_service.get_market_overview()

            logger.info(f"✅ 天枢星真实市场分析完成")

            return {
                "market_sentiment": sentiment_result,
                "market_data": market_data,
                "analysis_time": datetime.now().isoformat(),
                "data_source": "tianshu_real_services"
            }

        except Exception as e:
            logger.error(f"天枢星真实市场分析失败: {e}")
            return {"analysis_status": "failed", "error": str(e)}

    async def _real_kaiyang_trading_selection(self, session: Dict[str, Any], market_analysis: Dict[str, Any]) -> List[str]:
        """开阳星真实实盘选股"""
        try:
            if not self.automation_engine.get("kaiyang_selection"):
                logger.warning("开阳星选股服务不可用")
                return []

            kaiyang_service = self.automation_engine["kaiyang_selection"]

            # 基于市场分析的实盘选股
            selection_context = {
                "selection_type": "live_trading",
                "target_count": 5,  # 实盘交易保守选择
                "market_context": market_analysis.get("market_sentiment", {}),
                "risk_preference": "conservative",
                "trading_purpose": True,
                "requester": "瑶光星实盘交易系统"
            }

            result = await kaiyang_service.select_stocks(selection_context)

            if result.get("success"):
                selected_stocks = result.get("selection_result", {}).get("selected_stocks", [])
                stock_codes = [stock.get("stock_code") for stock in selected_stocks if stock.get("stock_code")]

                logger.info(f"✅ 开阳星真实实盘选股完成: {len(stock_codes)} 只股票")
                return stock_codes
            else:
                logger.warning(f"开阳星实盘选股失败: {result.get('error')}")
                return []

        except Exception as e:
            logger.error(f"开阳星真实实盘选股失败: {e}")
            return []

    async def _real_kaiyang_stock_selection(self, session: Dict[str, Any]) -> List[str]:
        """开阳星真实选股"""
        try:
            if not self.automation_engine.get("kaiyang_selection"):
                logger.warning("开阳星选股服务不可用，使用备用方案")
                return ["000001.XSHE", "600519.XSHG", "000858.XSHE"]

            # 调用真实的开阳星选股服务
            kaiyang_service = self.automation_engine["kaiyang_selection"]

            selection_context = {
                "selection_type": "learning_mode",
                "target_count": 1,  # 学习模式只选择1只股票
                "market_context": {
                    "sentiment": 0.6,
                    "volatility": "medium",
                    "trend": "neutral"
                },
                "learning_purpose": True,
                "requester": "瑶光星统一学习系统"
            }

            result = await kaiyang_service.select_stocks(selection_context)

            if result.get("success"):
                selected_stocks = result.get("selection_result", {}).get("selected_stocks", [])
                stock_codes = [stock.get("stock_code") for stock in selected_stocks if stock.get("stock_code")]

                logger.info(f"✅ 开阳星真实选股完成: {len(stock_codes)} 只股票")
                return stock_codes
            else:
                logger.warning(f"开阳星选股失败: {result.get('error')}")
                return ["000001.XSHE", "600519.XSHG"]

        except Exception as e:
            logger.error(f"开阳星真实选股失败: {e}")
            return ["000001.XSHE", "600519.XSHG"]

    async def _real_data_collection(self, session: Dict[str, Any], stock_codes: List[str]) -> Dict[str, Any]:
        """瑶光星真实数据收集"""
        try:
            if not self.automation_engine.get("data_collection"):
                logger.warning("数据收集服务不可用，使用本地数据")
                return {"successful_stocks": stock_codes, "data_source": "local_fallback"}

            # 调用真实的数据收集服务
            data_service = self.automation_engine["data_collection"]

            # 收集历史数据
            years = session["config"].get("data_years", 10)
            result = await data_service.collect_ten_year_data(
                stock_codes=stock_codes,
                batch_size=3,
                delay_between_batches=1.0
            )

            if result.get("success"):
                collected_count = result.get("collected_count", len(stock_codes))
                logger.info(f"✅ 瑶光星真实数据收集完成: {collected_count} 只股票")
                return {
                    "successful_stocks": stock_codes[:collected_count],
                    "total_records": result.get("total_records", 0),
                    "data_source": result.get("data_source", "local_database"),
                    "collection_method": result.get("collection_method", "local_db")
                }
            else:
                logger.warning(f"数据收集失败: {result.get('error')}")
                return {"successful_stocks": stock_codes, "data_source": "local_fallback"}

        except Exception as e:
            logger.error(f"瑶光星真实数据收集失败: {e}")
            return {"successful_stocks": stock_codes, "data_source": "local_fallback"}

    async def _real_tianshu_market_info_collection(self, session: Dict[str, Any], stock_codes: List[str]) -> Dict[str, Any]:
        """天枢星真实市场信息收集"""
        try:
            logger.info(f"📊 天枢星开始收集 {len(stock_codes)} 只股票的市场信息")

            # 调用天枢星整个角色的自动化系统
            try:
                from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
                tianshu_available = True
                logger.info("✅ 天枢星自动化系统可用")
            except ImportError:
                logger.warning("天枢星自动化系统不可用，使用本地数据分析")
                tianshu_available = False

            market_info_results = {}

            for stock_code in stock_codes:
                try:
                    logger.info(f"📰 天枢星收集 {stock_code} 市场信息...")

                    if tianshu_available:
                        # 调用天枢星整个角色的自动化系统
                        automation_context = {
                            "stock_code": stock_code,
                            "task_type": "market_info_collection",
                            "session_id": session["session_id"],
                            "analysis_depth": "comprehensive"
                        }

                        automation_result = await tianshu_automation_system.execute_market_analysis(automation_context)

                        market_info_results[stock_code] = {
                            "automation_result": automation_result,
                            "collection_time": datetime.now().isoformat(),
                            "data_source": "tianshu_automation_system"
                        }

                        logger.info(f"✅ {stock_code} 天枢星自动化系统执行完成")
                    else:
                        # 使用本地数据作为备用
                        market_info_results[stock_code] = await self._get_local_market_info(stock_code)
                        logger.info(f"✅ {stock_code} 天枢星本地数据收集完成")

                except Exception as e:
                    logger.warning(f"❌ {stock_code} 天枢星市场信息收集失败: {e}")
                    # 使用本地数据作为备用
                    market_info_results[stock_code] = await self._get_local_market_info(stock_code)

            logger.info(f"✅ 天枢星市场信息收集完成: {len(market_info_results)} 只股票")

            return {
                "market_info_results": market_info_results,
                "total_collected": len(market_info_results),
                "collection_method": "tianshu_real_services",
                "collection_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"天枢星市场信息收集失败: {e}")
            return {"market_info_results": {}, "total_collected": 0, "error": str(e)}

    async def _fallback_market_info_collection(self, stock_codes: List[str]) -> Dict[str, Any]:
        """备用市场信息收集"""
        market_info_results = {}

        for stock_code in stock_codes:
            market_info_results[stock_code] = await self._get_local_market_info(stock_code)

        return {
            "market_info_results": market_info_results,
            "total_collected": len(market_info_results),
            "collection_method": "local_fallback"
        }

    async def _get_local_market_info(self, stock_code: str) -> Dict[str, Any]:
        """从本地数据库获取市场信息"""
        try:
            import sqlite3
            import os

            db_path = os.path.join("backend", "data", "stock_database.db")
            clean_code = stock_code.replace('.XSHE', '').replace('.XSHG', '')

            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # 获取股票基本信息
            cursor.execute("SELECT * FROM stock_info WHERE stock_code = ?", (clean_code,))
            stock_info = cursor.fetchone()

            # 获取最近的交易数据
            cursor.execute("""
                SELECT * FROM daily_data
                WHERE stock_code = ?
                ORDER BY trade_date DESC
                LIMIT 20
            """, (clean_code,))
            recent_data = cursor.fetchall()

            conn.close()

            if stock_info and recent_data:
                # 计算市场指标
                prices = [float(row['close_price']) for row in recent_data if row['close_price']]
                volumes = [int(row['volume']) for row in recent_data if row['volume']]

                current_price = prices[0] if prices else 0
                avg_price_5d = sum(prices[:5]) / 5 if len(prices) >= 5 else current_price
                avg_volume = sum(volumes) / len(volumes) if volumes else 0

                return {
                    "stock_name": stock_info['stock_name'] if 'stock_name' in stock_info.keys() else '未知',
                    "industry": stock_info['industry'] if 'industry' in stock_info.keys() else '未知',
                    "market_cap": stock_info['market_cap'] if 'market_cap' in stock_info.keys() else 0,
                    "current_price": current_price,
                    "price_change_5d": (current_price - avg_price_5d) / avg_price_5d if avg_price_5d > 0 else 0,
                    "avg_volume": avg_volume,
                    "data_points": len(recent_data),
                    "data_source": "local_database"
                }
            else:
                return {
                    "stock_name": "未知",
                    "industry": "未知",
                    "current_price": 0,
                    "data_source": "local_database",
                    "error": "无基本信息"
                }

        except Exception as e:
            logger.error(f"获取本地市场信息失败: {e}")
            return {"error": str(e), "data_source": "local_database"}

    async def _get_stock_fundamental_info(self, stock_code: str) -> Dict[str, Any]:
        """获取股票基本面信息"""
        return await self._get_local_market_info(stock_code)

    async def _real_tianji_risk_analysis(self, session: Dict[str, Any], data_results: Dict[str, Any]) -> Dict[str, Any]:
        """天玑星真实风险分析"""
        try:
            logger.info(f"⚠️ 天玑星开始风险分析")

            successful_stocks = data_results.get("successful_stocks", [])
            risk_analysis_results = {}

            # 调用天玑星整个角色的自动化系统
            try:
                from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
                tianji_available = True
                logger.info("✅ 天玑星自动化系统可用")
            except ImportError:
                logger.warning("天玑星自动化系统不可用，使用内置风险分析")
                tianji_available = False

            for stock_code in successful_stocks:
                try:
                    if tianji_available:
                        # 调用天玑星整个角色的自动化系统
                        automation_context = {
                            "stock_code": stock_code,
                            "task_type": "comprehensive_risk_analysis",
                            "session_id": session["session_id"],
                            "position_size": 100000,  # 10万仓位
                            "market_context": {
                                "market_trend": "neutral",
                                "volatility_regime": "normal"
                            }
                        }

                        automation_result = await tianji_automation_system.execute_risk_analysis(automation_context)

                        risk_analysis_results[stock_code] = {
                            "automation_result": automation_result,
                            "analysis_time": datetime.now().isoformat(),
                            "data_source": "tianji_automation_system"
                        }

                        risk_level = automation_result.get("risk_assessment", {}).get("risk_level", "未知")
                        logger.info(f"✅ {stock_code} 天玑星自动化风险分析完成: 风险等级 {risk_level}")
                    else:
                        # 使用内置分析作为备用
                        historical_data = await self._get_stock_historical_data(stock_code)
                        detailed_risk = await self._calculate_detailed_risk_metrics(stock_code, historical_data)

                        risk_analysis_results[stock_code] = {
                            "detailed_metrics": detailed_risk,
                            "analysis_time": datetime.now().isoformat(),
                            "data_source": "internal_fallback"
                        }

                        logger.info(f"✅ {stock_code} 天玑星内置风险分析完成: 风险等级 {detailed_risk.get('risk_level', '未知')}")

                except Exception as e:
                    logger.error(f"❌ {stock_code} 天玑星风险分析失败: {e}")
                    # 使用内置分析作为备用
                    risk_analysis_results[stock_code] = await self._fallback_stock_risk_analysis(stock_code)

            logger.info(f"✅ 天玑星风险分析完成: {len(risk_analysis_results)} 只股票")

            return {
                "risk_analysis_results": risk_analysis_results,
                "total_analyzed": len(risk_analysis_results),
                "analysis_method": "tianji_real_service",
                "analysis_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"天玑星风险分析失败: {e}")
            return {"risk_analysis_results": {}, "total_analyzed": 0, "error": str(e)}

    async def _fallback_risk_analysis(self, stock_codes: List[str]) -> Dict[str, Any]:
        """备用风险分析"""
        risk_analysis_results = {}

        for stock_code in stock_codes:
            risk_analysis_results[stock_code] = await self._fallback_stock_risk_analysis(stock_code)

        return {
            "risk_analysis_results": risk_analysis_results,
            "total_analyzed": len(risk_analysis_results),
            "analysis_method": "fallback_internal"
        }

    async def _fallback_stock_risk_analysis(self, stock_code: str) -> Dict[str, Any]:
        """单只股票的备用风险分析"""
        try:
            historical_data = await self._get_stock_historical_data(stock_code)
            detailed_risk = await self._calculate_detailed_risk_metrics(stock_code, historical_data)

            return {
                "detailed_metrics": detailed_risk,
                "analysis_time": datetime.now().isoformat(),
                "data_source": "internal_fallback"
            }
        except Exception as e:
            return {"error": str(e), "data_source": "internal_fallback"}

    async def _calculate_detailed_risk_metrics(self, stock_code: str, historical_data: List[Dict]) -> Dict[str, Any]:
        """计算详细风险指标"""
        try:
            if not historical_data:
                return {"risk_level": "无法评估", "error": "无历史数据"}

            # 提取价格数据
            prices = [float(d.get('close_price', 0)) for d in historical_data if d.get('close_price')]

            if len(prices) < 20:
                return {"risk_level": "数据不足", "error": "历史数据不足"}

            # 计算收益率
            returns = []
            for i in range(1, len(prices)):
                if prices[i-1] > 0:
                    ret = (prices[i] - prices[i-1]) / prices[i-1]
                    returns.append(ret)

            if not returns:
                return {"risk_level": "无法计算", "error": "无有效收益率"}

            # 计算风险指标
            import numpy as np

            returns_array = np.array(returns)

            # 波动率（年化）
            volatility = np.std(returns_array) * np.sqrt(252)

            # VaR (95%)
            var_95 = np.percentile(returns_array, 5)

            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            peak = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - peak) / peak
            max_drawdown = np.min(drawdown)

            # 夏普比率（假设无风险利率为3%）
            risk_free_rate = 0.03 / 252  # 日无风险利率
            excess_returns = returns_array - risk_free_rate
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if np.std(excess_returns) > 0 else 0

            # 风险等级评估
            if volatility > 0.4:
                risk_level = "高风险"
            elif volatility > 0.25:
                risk_level = "中等风险"
            elif volatility > 0.15:
                risk_level = "低风险"
            else:
                risk_level = "极低风险"

            return {
                "volatility": float(volatility),
                "var_95": float(var_95),
                "max_drawdown": float(max_drawdown),
                "sharpe_ratio": float(sharpe_ratio),
                "risk_level": risk_level,
                "data_points": len(returns),
                "analysis_period": f"{len(returns)}天"
            }

        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return {"risk_level": "计算失败", "error": str(e)}

    async def _get_stock_historical_data(self, stock_code: str) -> List[Dict]:
        """获取股票历史数据"""
        try:
            import sqlite3
            import os

            db_path = os.path.join("backend", "data", "stock_database.db")
            clean_code = stock_code.replace('.XSHE', '').replace('.XSHG', '')

            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM daily_data
                WHERE stock_code = ?
                ORDER BY trade_date DESC
                LIMIT 252
            """, (clean_code,))

            rows = cursor.fetchall()
            conn.close()

            return [dict(row) for row in rows]

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return []

    async def _real_tianxuan_technical_analysis(self, session: Dict[str, Any], data_results: Dict[str, Any]) -> Dict[str, Any]:
        """天璇星真实技术分析"""
        try:
            logger.info(f"📈 天璇星开始技术分析")

            successful_stocks = data_results.get("successful_stocks", [])
            technical_analysis_results = {}

            # 调用天璇星整个角色的自动化系统
            try:
                from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
                tianxuan_available = True
                logger.info("✅ 天璇星自动化系统可用")
            except ImportError:
                logger.warning("天璇星自动化系统不可用，使用内置技术分析")
                tianxuan_available = False

            for stock_code in successful_stocks:
                try:
                    if tianxuan_available:
                        # 调用天璇星整个角色的自动化系统
                        automation_context = {
                            "stock_code": stock_code,
                            "task_type": "comprehensive_technical_analysis",
                            "session_id": session["session_id"],
                            "analysis_type": "comprehensive",
                            "analysis_period": 60
                        }

                        automation_result = await tianxuan_automation_system.execute_technical_analysis(automation_context)

                        technical_analysis_results[stock_code] = {
                            "automation_result": automation_result,
                            "analysis_time": datetime.now().isoformat(),
                            "data_source": "tianxuan_automation_system"
                        }

                        trend_direction = automation_result.get("technical_analysis", {}).get("trend_direction", "未知")
                        logger.info(f"✅ {stock_code} 天璇星自动化技术分析完成: 趋势 {trend_direction}")
                    else:
                        # 使用内置分析作为备用
                        historical_data = await self._get_stock_historical_data(stock_code)
                        detailed_technical = await self._calculate_detailed_technical_indicators(stock_code, historical_data)

                        technical_analysis_results[stock_code] = {
                            "detailed_indicators": detailed_technical,
                            "analysis_time": datetime.now().isoformat(),
                            "data_source": "internal_fallback"
                        }

                        logger.info(f"✅ {stock_code} 天璇星内置技术分析完成: 趋势 {detailed_technical.get('trend_direction', '未知')}")

                except Exception as e:
                    logger.error(f"❌ {stock_code} 天璇星技术分析失败: {e}")
                    # 使用内置分析作为备用
                    technical_analysis_results[stock_code] = await self._fallback_stock_technical_analysis(stock_code)

            logger.info(f"✅ 天璇星技术分析完成: {len(technical_analysis_results)} 只股票")

            return {
                "technical_analysis_results": technical_analysis_results,
                "total_analyzed": len(technical_analysis_results),
                "analysis_method": "tianxuan_real_service",
                "analysis_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"天璇星技术分析失败: {e}")
            return {"technical_analysis_results": {}, "total_analyzed": 0, "error": str(e)}

    async def _fallback_technical_analysis(self, stock_codes: List[str]) -> Dict[str, Any]:
        """备用技术分析"""
        technical_analysis_results = {}

        for stock_code in stock_codes:
            technical_analysis_results[stock_code] = await self._fallback_stock_technical_analysis(stock_code)

        return {
            "technical_analysis_results": technical_analysis_results,
            "total_analyzed": len(technical_analysis_results),
            "analysis_method": "fallback_internal"
        }

    async def _fallback_stock_technical_analysis(self, stock_code: str) -> Dict[str, Any]:
        """单只股票的备用技术分析"""
        try:
            historical_data = await self._get_stock_historical_data(stock_code)
            detailed_technical = await self._calculate_detailed_technical_indicators(stock_code, historical_data)

            return {
                "detailed_indicators": detailed_technical,
                "analysis_time": datetime.now().isoformat(),
                "data_source": "internal_fallback"
            }
        except Exception as e:
            return {"error": str(e), "data_source": "internal_fallback"}

    async def _calculate_detailed_technical_indicators(self, stock_code: str, historical_data: List[Dict]) -> Dict[str, Any]:
        """计算详细技术指标"""
        try:
            if not historical_data or len(historical_data) < 20:
                return {"trend_direction": "数据不足", "error": "历史数据不足"}

            # 提取价格和成交量数据
            prices = [float(d.get('close_price', 0)) for d in historical_data if d.get('close_price')]
            highs = [float(d.get('high_price', 0)) for d in historical_data if d.get('high_price')]
            lows = [float(d.get('low_price', 0)) for d in historical_data if d.get('low_price')]
            volumes = [int(d.get('volume', 0)) for d in historical_data if d.get('volume')]

            if len(prices) < 20:
                return {"trend_direction": "数据不足", "error": "价格数据不足"}

            import numpy as np

            prices_array = np.array(prices)

            # 移动平均线
            ma_5 = np.mean(prices_array[:5]) if len(prices_array) >= 5 else prices_array[0]
            ma_10 = np.mean(prices_array[:10]) if len(prices_array) >= 10 else prices_array[0]
            ma_20 = np.mean(prices_array[:20]) if len(prices_array) >= 20 else prices_array[0]

            current_price = prices_array[0]

            # 趋势判断
            if current_price > ma_5 > ma_10 > ma_20:
                trend_direction = "强势上涨"
                trend_strength = 0.9
            elif current_price > ma_5 > ma_10:
                trend_direction = "温和上涨"
                trend_strength = 0.7
            elif current_price < ma_5 < ma_10 < ma_20:
                trend_direction = "强势下跌"
                trend_strength = 0.9
            elif current_price < ma_5 < ma_10:
                trend_direction = "温和下跌"
                trend_strength = 0.7
            else:
                trend_direction = "震荡整理"
                trend_strength = 0.5

            # RSI计算（简化版）
            if len(prices_array) >= 14:
                price_changes = np.diff(prices_array[:14])
                gains = np.where(price_changes > 0, price_changes, 0)
                losses = np.where(price_changes < 0, -price_changes, 0)

                avg_gain = np.mean(gains) if len(gains) > 0 else 0
                avg_loss = np.mean(losses) if len(losses) > 0 else 0.01  # 避免除零

                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            else:
                rsi = 50  # 中性值

            # 支撑阻力位
            recent_high = max(highs[:20]) if len(highs) >= 20 else current_price
            recent_low = min(lows[:20]) if len(lows) >= 20 else current_price

            # 成交量分析
            if volumes:
                avg_volume = np.mean(volumes[:10]) if len(volumes) >= 10 else volumes[0]
                volume_ratio = volumes[0] / avg_volume if avg_volume > 0 else 1.0
            else:
                volume_ratio = 1.0

            # 买卖信号
            if rsi < 30 and current_price < recent_low * 1.02:
                signal = "强烈买入"
                signal_strength = 0.9
            elif rsi > 70 and current_price > recent_high * 0.98:
                signal = "强烈卖出"
                signal_strength = 0.9
            elif trend_direction in ["强势上涨", "温和上涨"] and volume_ratio > 1.5:
                signal = "买入"
                signal_strength = 0.7
            elif trend_direction in ["强势下跌", "温和下跌"] and volume_ratio > 1.5:
                signal = "卖出"
                signal_strength = 0.7
            else:
                signal = "持有"
                signal_strength = 0.5

            return {
                "current_price": float(current_price),
                "ma_5": float(ma_5),
                "ma_10": float(ma_10),
                "ma_20": float(ma_20),
                "trend_direction": trend_direction,
                "trend_strength": float(trend_strength),
                "rsi": float(rsi),
                "support_level": float(recent_low),
                "resistance_level": float(recent_high),
                "volume_ratio": float(volume_ratio),
                "trading_signal": signal,
                "signal_strength": float(signal_strength),
                "data_points": len(prices)
            }

        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {"trend_direction": "计算失败", "error": str(e)}

    async def _real_yuheng_learning_trading(self, session: Dict[str, Any], debate_results: Dict[str, Any]) -> Dict[str, Any]:
        """玉衡星真实交易执行（学习模式）"""
        try:
            logger.info(f"💰 玉衡星开始学习模式交易执行")

            selected_stocks = session["results"].get("selected_stocks", [])
            trading_results = {}

            # 调用玉衡星整个角色的自动化交易系统
            try:
                from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
                yuheng_available = True
            except ImportError:
                logger.warning("玉衡星自动化系统不可用，使用内置学习交易")
                yuheng_available = False

            for stock_code in selected_stocks:
                try:
                    # 获取辩论结论和分析结果
                    debate_conclusion = debate_results.get("debate_conclusion", "")
                    participant_views = debate_results.get("participant_views", {})

                    # 获取技术分析和风险分析结果
                    technical_results = session["results"].get("technical_analysis", {}).get("technical_analysis_results", {})
                    risk_results = session["results"].get("risk_analysis", {}).get("risk_analysis_results", {})

                    # 构建玉衡星自动化交易上下文
                    trading_context = {
                        "stock_code": stock_code,
                        "trading_mode": "learning",  # 学习模式
                        "initial_capital": 100000,  # 初始资金10万
                        "max_position_size": 0.3,   # 最大仓位30%
                        "debate_conclusion": debate_conclusion,
                        "participant_views": participant_views,
                        "technical_analysis": technical_results.get(stock_code, {}),
                        "risk_analysis": risk_results.get(stock_code, {}),
                        "session_id": session["session_id"]
                    }

                    if yuheng_available:
                        # 调用玉衡星整个角色的自动化系统
                        execution_result = await yuheng_automation_system.execute_trading_automation(trading_context)

                        trading_results[stock_code] = {
                            "execution_result": execution_result,
                            "execution_time": datetime.now().isoformat(),
                            "data_source": "yuheng_automation_system"
                        }

                        # 提取交易统计
                        trades = execution_result.get("trades", [])
                        total_profit = execution_result.get("total_profit", 0)
                        total_trades = len(trades)

                        logger.info(f"✅ {stock_code} 玉衡星自动化交易完成: {total_trades}笔交易, 盈亏: {total_profit:.2f}元")
                    else:
                        # 使用内置学习交易系统
                        execution_result = await self._execute_real_learning_trading(stock_code, trading_context)

                        trading_results[stock_code] = {
                            "execution_result": execution_result,
                            "execution_time": datetime.now().isoformat(),
                            "data_source": "internal_learning_system"
                        }

                        # 提取交易统计
                        total_profit = execution_result.get("total_profit", 0)
                        total_trades = execution_result.get("total_trades", 0)

                        logger.info(f"✅ {stock_code} 内置学习交易完成: {total_trades}笔交易, 盈亏: {total_profit:.2f}元")

                except Exception as e:
                    logger.error(f"❌ {stock_code} 玉衡星交易执行失败: {e}")
                    trading_results[stock_code] = {
                        "error": str(e),
                        "execution_time": datetime.now().isoformat(),
                        "data_source": "error"
                    }

            logger.info(f"✅ 玉衡星学习模式交易执行完成: {len(trading_results)} 只股票")

            # 计算总体交易统计
            total_stats = self._calculate_trading_statistics(trading_results)

            return {
                "trading_results": trading_results,
                "total_executed": len(trading_results),
                "trading_statistics": total_stats,
                "execution_method": "yuheng_real_service",
                "execution_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"玉衡星学习模式交易执行失败: {e}")
            return {"trading_results": {}, "total_executed": 0, "error": str(e)}

    async def _fallback_learning_trading(self, stock_codes: List[str], debate_results: Dict[str, Any]) -> Dict[str, Any]:
        """备用学习交易"""
        trading_results = {}

        for stock_code in stock_codes:
            trading_results[stock_code] = await self._fallback_stock_trading(stock_code, debate_results)

        total_stats = self._calculate_trading_statistics(trading_results)

        return {
            "trading_results": trading_results,
            "total_executed": len(trading_results),
            "trading_statistics": total_stats,
            "execution_method": "fallback_simulation"
        }

    async def _fallback_stock_trading(self, stock_code: str, debate_results: Dict[str, Any]) -> Dict[str, Any]:
        """单只股票的备用交易"""
        try:
            # 构建简化的交易上下文
            trading_context = {
                "stock_code": stock_code,
                "mode": "learning",
                "debate_conclusion": debate_results.get("debate_conclusion", ""),
                "initial_capital": 100000,
                "max_position_size": 0.3
            }

            simulation_result = await self._execute_learning_simulation(stock_code, trading_context)

            return {
                "simulation_result": simulation_result,
                "execution_time": datetime.now().isoformat(),
                "data_source": "internal_fallback"
            }
        except Exception as e:
            return {"error": str(e), "data_source": "internal_fallback"}

    async def _execute_learning_simulation(self, stock_code: str, trading_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行学习模式交易模拟"""
        try:
            # 获取当前价格和历史数据
            historical_data = await self._get_stock_historical_data(stock_code)

            if not historical_data:
                return {"action": "无操作", "reason": "无历史数据", "profit_loss": 0}

            current_price = float(historical_data[0].get('close_price', 0))
            if current_price <= 0:
                return {"action": "无操作", "reason": "无效价格", "profit_loss": 0}

            # 分析辩论结论决定交易动作
            debate_conclusion = trading_context.get("debate_conclusion", "")
            participant_views = trading_context.get("participant_views", {})

            # 统计四星观点
            buy_signals = 0
            sell_signals = 0
            hold_signals = 0

            for star_name, view in participant_views.items():
                position = view.get("position", "").lower()
                if "买入" in position or "看好" in position or "增加" in position:
                    buy_signals += 1
                elif "卖出" in position or "谨慎" in position or "减少" in position:
                    sell_signals += 1
                else:
                    hold_signals += 1

            # 决定交易动作
            initial_capital = trading_context.get("initial_capital", 100000)
            max_position_size = trading_context.get("max_position_size", 0.3)
            max_investment = initial_capital * max_position_size

            if buy_signals >= 2:  # 至少2星支持买入
                action = "买入"
                shares = int(max_investment / current_price / 100) * 100  # 整手买入
                investment = shares * current_price

                # 模拟持有一段时间后的收益（使用历史数据模拟）
                if len(historical_data) >= 10:
                    future_price = float(historical_data[9].get('close_price', current_price))
                    profit_loss = (future_price - current_price) * shares
                else:
                    profit_loss = current_price * shares * 0.02  # 假设2%收益

                return {
                    "action": action,
                    "shares": shares,
                    "price": current_price,
                    "investment": investment,
                    "profit_loss": profit_loss,
                    "return_rate": profit_loss / investment if investment > 0 else 0,
                    "reason": f"{buy_signals}星支持买入",
                    "holding_period": "10天模拟"
                }

            elif sell_signals >= 2:  # 至少2星支持卖出
                action = "卖出"
                # 假设之前持有股票
                shares = int(max_investment / current_price / 100) * 100
                revenue = shares * current_price

                # 模拟卖出收益（假设之前以更高价格买入）
                buy_price = current_price * 1.05  # 假设之前高5%买入
                profit_loss = (current_price - buy_price) * shares

                return {
                    "action": action,
                    "shares": shares,
                    "price": current_price,
                    "revenue": revenue,
                    "profit_loss": profit_loss,
                    "return_rate": profit_loss / (buy_price * shares) if buy_price > 0 else 0,
                    "reason": f"{sell_signals}星支持卖出",
                    "holding_period": "模拟卖出"
                }

            else:  # 持有观望
                action = "持有"
                return {
                    "action": action,
                    "reason": f"观点分歧，{hold_signals}星建议持有",
                    "profit_loss": 0,
                    "current_price": current_price
                }

        except Exception as e:
            logger.error(f"执行学习交易模拟失败: {e}")
            return {"action": "无操作", "error": str(e), "profit_loss": 0}

    def _calculate_trading_statistics(self, trading_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算交易统计"""
        try:
            total_trades = 0
            total_profit_loss = 0
            buy_trades = 0
            sell_trades = 0
            hold_trades = 0

            for stock_code, result in trading_results.items():
                simulation = result.get("simulation_result", {})
                action = simulation.get("action", "")
                profit_loss = simulation.get("profit_loss", 0)

                if action in ["买入", "卖出"]:
                    total_trades += 1
                    total_profit_loss += profit_loss

                    if action == "买入":
                        buy_trades += 1
                    elif action == "卖出":
                        sell_trades += 1
                else:
                    hold_trades += 1

            return {
                "total_stocks": len(trading_results),
                "total_trades": total_trades,
                "buy_trades": buy_trades,
                "sell_trades": sell_trades,
                "hold_trades": hold_trades,
                "total_profit_loss": total_profit_loss,
                "average_profit_loss": total_profit_loss / total_trades if total_trades > 0 else 0,
                "win_rate": 0.6 if total_profit_loss > 0 else 0.4  # 简化胜率计算
            }

        except Exception as e:
            logger.error(f"计算交易统计失败: {e}")
            return {"total_stocks": 0, "total_trades": 0, "total_profit_loss": 0}

    async def _execute_real_learning_trading(self, stock_code: str, trading_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行真实的学习模式交易"""
        try:
            logger.info(f"💰 开始{stock_code}的真实学习交易")

            # 获取股票历史数据
            historical_data = await self._get_stock_historical_data(stock_code)

            if not historical_data or len(historical_data) < 20:
                return {"error": "历史数据不足", "total_profit": 0, "total_trades": 0}

            # 初始化交易参数
            initial_capital = trading_context.get("initial_capital", 100000)
            max_position_size = trading_context.get("max_position_size", 0.3)
            current_capital = initial_capital
            current_position = 0  # 当前持仓
            trades = []

            # 分析四星辩论结果制定交易策略
            participant_views = trading_context.get("participant_views", {})
            technical_analysis = trading_context.get("technical_analysis", {})
            risk_analysis = trading_context.get("risk_analysis", {})

            # 统计四星观点
            buy_signals = 0
            sell_signals = 0
            hold_signals = 0

            for star_name, view in participant_views.items():
                position = view.get("position", "").lower()
                if any(word in position for word in ["买入", "看好", "增加", "配置"]):
                    buy_signals += 1
                elif any(word in position for word in ["卖出", "谨慎", "减少", "降低"]):
                    sell_signals += 1
                else:
                    hold_signals += 1

            # 获取当前价格和技术指标
            current_price = float(historical_data[0].get('close_price', 0))
            if current_price <= 0:
                return {"error": "无效价格数据", "total_profit": 0, "total_trades": 0}

            # 计算技术指标
            prices = [float(d.get('close_price', 0)) for d in historical_data[:20] if d.get('close_price')]
            ma_5 = sum(prices[:5]) / 5 if len(prices) >= 5 else current_price
            ma_20 = sum(prices[:20]) / 20 if len(prices) >= 20 else current_price

            # 制定交易决策
            trading_decision = self._make_trading_decision(
                buy_signals, sell_signals, hold_signals,
                current_price, ma_5, ma_20,
                technical_analysis, risk_analysis
            )

            # 执行交易
            if trading_decision["action"] == "买入" and current_position == 0:
                # 买入操作
                investment_amount = min(current_capital * max_position_size, current_capital)
                shares = int(investment_amount / current_price / 100) * 100  # 整手买入
                actual_investment = shares * current_price

                if shares > 0 and actual_investment <= current_capital:
                    current_position = shares
                    current_capital -= actual_investment

                    trade = {
                        "action": "买入",
                        "price": current_price,
                        "shares": shares,
                        "amount": actual_investment,
                        "timestamp": datetime.now().isoformat(),
                        "reason": trading_decision["reason"]
                    }
                    trades.append(trade)

                    logger.info(f"📈 买入 {shares} 股，价格 ¥{current_price:.2f}，金额 ¥{actual_investment:.2f}")

            elif trading_decision["action"] == "卖出" and current_position > 0:
                # 卖出操作
                sell_amount = current_position * current_price
                current_capital += sell_amount

                trade = {
                    "action": "卖出",
                    "price": current_price,
                    "shares": current_position,
                    "amount": sell_amount,
                    "timestamp": datetime.now().isoformat(),
                    "reason": trading_decision["reason"]
                }
                trades.append(trade)

                logger.info(f"📉 卖出 {current_position} 股，价格 ¥{current_price:.2f}，金额 ¥{sell_amount:.2f}")
                current_position = 0

            # 计算当前总资产和盈亏
            current_asset_value = current_capital + (current_position * current_price)
            total_profit = current_asset_value - initial_capital
            profit_rate = (total_profit / initial_capital) * 100

            # 生成交易统计
            trading_stats = {
                "initial_capital": initial_capital,
                "current_capital": current_capital,
                "current_position": current_position,
                "current_price": current_price,
                "current_asset_value": current_asset_value,
                "total_profit": total_profit,
                "profit_rate": profit_rate,
                "total_trades": len(trades),
                "trades": trades,
                "trading_decision": trading_decision,
                "four_stars_analysis": {
                    "buy_signals": buy_signals,
                    "sell_signals": sell_signals,
                    "hold_signals": hold_signals
                }
            }

            return trading_stats

        except Exception as e:
            logger.error(f"真实学习交易执行失败: {e}")
            return {"error": str(e), "total_profit": 0, "total_trades": 0}

    def _make_trading_decision(self, buy_signals: int, sell_signals: int, hold_signals: int,
                              current_price: float, ma_5: float, ma_20: float,
                              technical_analysis: Dict, risk_analysis: Dict) -> Dict[str, Any]:
        """制定交易决策"""
        try:
            # 基于四星观点的基础决策
            if buy_signals >= 2:  # 至少2星支持买入
                base_action = "买入"
                base_reason = f"{buy_signals}星支持买入"
            elif sell_signals >= 2:  # 至少2星支持卖出
                base_action = "卖出"
                base_reason = f"{sell_signals}星支持卖出"
            else:
                base_action = "持有"
                base_reason = f"观点分歧，{hold_signals}星建议持有"

            # 技术分析修正
            technical_modifier = ""
            if current_price > ma_5 > ma_20:
                technical_modifier = "技术面支持"
            elif current_price < ma_5 < ma_20:
                technical_modifier = "技术面谨慎"
            else:
                technical_modifier = "技术面中性"

            # 风险分析修正
            risk_level = risk_analysis.get("detailed_metrics", {}).get("risk_level", "未知")
            risk_modifier = f"风险等级: {risk_level}"

            # 综合决策
            final_reason = f"{base_reason}, {technical_modifier}, {risk_modifier}"

            return {
                "action": base_action,
                "reason": final_reason,
                "confidence": 0.7 + (max(buy_signals, sell_signals) * 0.1),
                "technical_support": technical_modifier,
                "risk_assessment": risk_modifier
            }

        except Exception as e:
            logger.error(f"交易决策制定失败: {e}")
            return {
                "action": "持有",
                "reason": "决策失败，保持观望",
                "confidence": 0.5
            }

    async def _real_tianquan_strategy_testing(self, session: Dict[str, Any], data_results: Dict[str, Any]) -> Dict[str, Any]:
        """天权星真实战法测试"""
        try:
            # 调用天权星整个角色的自动化系统
            try:
                from roles.tianquan_star.core.tianquan_automation_system import tianquan_automation_system
                tianquan_available = True
                logger.info("✅ 天权星自动化系统可用")
            except ImportError:
                logger.warning("天权星自动化系统不可用，使用内置战法测试")
                tianquan_available = False

            # 调用真实的天权星战法服务
            tianquan_service = self.automation_engine["tianquan_strategies"]
            successful_stocks = data_results.get("successful_stocks", [])

            strategy_results = {}

            for stock_code in successful_stocks:
                # 获取股票的历史数据用于回测
                historical_data = await self._get_stock_historical_data(stock_code)

                # 为每只股票测试战法
                strategy_request = {
                    "strategy_type": "trend_following",
                    "stock_code": stock_code,
                    "execution_mode": "learning",
                    "learning_context": True,
                    "historical_data": historical_data,  # 传递真实历史数据
                    "strategy_config": {
                        "parameters": {
                            "ma_short": 5,
                            "ma_long": 20
                        }
                    }
                }

                result = await tianquan_service.execute_strategy_backtest(strategy_request)

                if result.get("success"):
                    execution_result = result.get("execution_result", {})
                    strategy_results[stock_code] = execution_result

                    # 提取真实收益信息
                    total_return = execution_result.get("total_return", 0.0)
                    total_trades = execution_result.get("total_trades", 0)
                    win_rate = execution_result.get("win_rate", 0.0)

                    logger.info(f"✅ {stock_code} 天权星战法测试完成: 收益{total_return:.2%}, 交易{total_trades}次, 胜率{win_rate:.1%}")
                else:
                    logger.warning(f"❌ {stock_code} 天权星战法测试失败: {result.get('error', '未知错误')}")

            # 找出最佳战法
            best_strategy = None
            best_return = -float('inf')

            for stock_code, result in strategy_results.items():
                total_return = result.get("total_return", 0)
                if total_return > best_return:
                    best_return = total_return
                    best_strategy = {
                        "stock_code": stock_code,
                        "strategy_type": result.get("strategy_type", "trend_following"),
                        "total_return": total_return,
                        "win_rate": result.get("win_rate", 0)
                    }

            logger.info(f"✅ 天权星真实战法测试完成: {len(strategy_results)} 只股票")

            return {
                "tested_strategies": strategy_results,
                "best_strategy": best_strategy,
                "total_tested": len(strategy_results)
            }

        except Exception as e:
            logger.error(f"天权星真实战法测试失败: {e}")
            return {"tested_strategies": {}, "best_strategy": None}

    async def _real_four_stars_debate(self, session: Dict[str, Any], strategy_results: Dict[str, Any]) -> Dict[str, Any]:
        """四星真实深度辩论"""
        try:
            if not self.automation_engine.get("four_stars_debate"):
                logger.warning("四星辩论系统不可用")
                return {"debate_conclusion": "系统不可用", "consensus_reached": False}

            # 调用真实的四星辩论系统
            debate_service = self.automation_engine["four_stars_debate"]

            # 获取选择的股票
            selected_stocks = session["results"].get("selected_stocks", [])
            if not selected_stocks:
                return {"debate_conclusion": "无选择股票可辩论", "consensus_reached": False}

            # 使用第一只股票进行辩论（学习模式只有一只）
            target_stock = selected_stocks[0]

            # 构建辩论议题 - 即使没有最佳策略也要进行辩论
            best_strategy = strategy_results.get("best_strategy")
            tested_strategies = strategy_results.get("tested_strategies", {})

            if best_strategy:
                # 有策略结果的情况
                debate_topic = {
                    "topic_type": "strategy_evaluation",
                    "subject": f"{best_strategy['stock_code']} 的 {best_strategy['strategy_type']} 战法分析",
                    "context": {
                        "stock_code": best_strategy["stock_code"],
                        "strategy_type": best_strategy["strategy_type"],
                        "total_return": best_strategy["total_return"],
                        "win_rate": best_strategy["win_rate"],
                        "tested_strategies": tested_strategies,
                        "has_strategy_results": True
                    },
                    "debate_purpose": "learning_optimization",
                    "requester": "瑶光星学习系统"
                }
            else:
                # 没有策略结果的情况 - 进行股票基本面分析辩论
                debate_topic = {
                    "topic_type": "stock_analysis",
                    "subject": f"{target_stock} 股票投资价值分析",
                    "context": {
                        "stock_code": target_stock,
                        "analysis_type": "fundamental_and_technical",
                        "tested_strategies": tested_strategies,
                        "has_strategy_results": False,
                        "data_source": "local_database",
                        "analysis_period": "10_years"
                    },
                    "debate_purpose": "investment_decision",
                    "requester": "瑶光星学习系统"
                }

            logger.info(f"🗣️ 启动四星辩论: {debate_topic['subject']}")

            # 启动真实辩论
            debate_result = await debate_service.start_debate_session(debate_topic)

            if debate_result.get("success"):
                logger.info(f"✅ 四星真实辩论完成: {debate_result.get('session_id')}")

                # 提取参与者观点
                participant_views = debate_result.get("participant_views", {})

                # 记录四星的具体分析
                four_stars_analysis = {}
                for star_name, view in participant_views.items():
                    four_stars_analysis[star_name] = {
                        "role": view.get("role", ""),
                        "position": view.get("position", ""),
                        "reasoning": view.get("reasoning", ""),
                        "confidence": view.get("confidence", 0.0)
                    }

                logger.info(f"📊 四星分析结果:")
                for star_name, analysis in four_stars_analysis.items():
                    logger.info(f"   {star_name}: {analysis['position']} (置信度: {analysis['confidence']:.2f})")

                return {
                    "debate_session_id": debate_result.get("session_id"),
                    "debate_conclusion": debate_result.get("conclusion", ""),
                    "consensus_reached": debate_result.get("consensus_reached", False),
                    "participant_views": participant_views,
                    "four_stars_analysis": four_stars_analysis,
                    "final_recommendation": debate_result.get("final_recommendation", ""),
                    "debate_rounds": debate_result.get("debate_rounds", 0),
                    "target_stock": target_stock
                }
            else:
                logger.warning(f"四星辩论失败: {debate_result.get('error')}")
                return {"debate_conclusion": "辩论失败", "consensus_reached": False}

        except Exception as e:
            logger.error(f"四星真实辩论失败: {e}")
            return {"debate_conclusion": "辩论异常", "consensus_reached": False}

    async def _real_learning_optimization(self, session: Dict[str, Any], debate_results: Dict[str, Any]) -> Dict[str, Any]:
        """瑶光星真实学习优化"""
        try:
            # 导入真实的学习优化服务
            from ..services.learning_optimization_service import learning_optimization_service

            # 获取辩论结论
            debate_conclusion = debate_results.get("debate_conclusion", "")
            consensus_reached = debate_results.get("consensus_reached", False)

            if not consensus_reached:
                logger.warning("四星辩论未达成共识，学习优化效果可能有限")

            # 执行真实的学习优化
            selected_stocks = session["results"].get("selected_stocks", [])
            optimization_results = {}

            for stock_code in selected_stocks:
                # 为每只股票执行学习优化
                result = await learning_optimization_service.optimize_stock_learning(
                    stock_code=stock_code,
                    learning_context={
                        "debate_conclusion": debate_conclusion,
                        "consensus_reached": consensus_reached,
                        "session_id": session["session_id"],
                        "optimization_purpose": "strategy_improvement"
                    }
                )

                if result.get("success"):
                    optimization_results[stock_code] = result
                    logger.info(f"✅ {stock_code} 学习优化完成")
                else:
                    logger.warning(f"❌ {stock_code} 学习优化失败")

            logger.info(f"✅ 瑶光星真实学习优化完成: {len(optimization_results)} 只股票")

            return {
                "optimization_results": optimization_results,
                "total_optimized": len(optimization_results),
                "learning_insights": [result.get("optimization_suggestions", []) for result in optimization_results.values()],
                "performance_improvement": sum(result.get("learning_result", {}).get("performance_impact", 0) for result in optimization_results.values())
            }

        except Exception as e:
            logger.error(f"瑶光星真实学习优化失败: {e}")
            return {"optimization_results": {}, "total_optimized": 0}

    async def _real_rd_agent_research(self, session: Dict[str, Any], learning_results: Dict[str, Any]) -> Dict[str, Any]:
        """RD-Agent真实因子研究"""
        try:
            # 导入真实的RD-Agent集成服务
            from ..services.rd_agent_integration_service import rd_agent_integration_service

            # 获取学习优化结果
            optimization_results = learning_results.get("optimization_results", {})

            if not optimization_results:
                logger.warning("无学习优化结果，跳过RD-Agent因子研究")
                return {"factor_research_results": {}, "new_factors": []}

            # 执行真实的因子研究
            factor_results = {}
            new_factors = []

            for stock_code, optimization_result in optimization_results.items():
                # 为每只股票进行因子研究
                research_config = {
                    "stock_code": stock_code,
                    "research_type": "factor_generation",
                    "optimization_context": optimization_result,
                    "target_ic": 0.05,
                    "max_iterations": 3
                }

                # 调用真实的RD-Agent服务
                try:
                    from roles.yaoguang_star.services.rd_agent_integration_service import rd_agent_integration_service
                    factors = await rd_agent_integration_service.generate_new_factors(research_config)

                    if factors:
                        factor_results[stock_code] = factors
                        new_factors.extend(factors)
                        logger.info(f"✅ {stock_code} RD-Agent因子研究完成: {len(factors)} 个因子")
                    else:
                        logger.error(f"❌ {stock_code} RD-Agent因子研究返回空结果")
                        raise RuntimeError(f"{stock_code} RD-Agent因子研究失败")

                except Exception as e:
                    logger.error(f"RD-Agent服务调用失败: {e}")
                    raise RuntimeError(f"{stock_code} RD-Agent因子研究失败: {e}")

            # 获取Alpha158因子作为补充
            try:
                alpha158_factors = await rd_agent_integration_service.get_alpha158_factors()
            except Exception as e:
                logger.error(f"获取Alpha158因子失败: {e}")
                alpha158_factors = []

            logger.info(f"✅ RD-Agent因子研究完成: {len(new_factors)} 个新因子, {len(alpha158_factors)} 个Alpha158因子")

            return {
                "factor_research_results": factor_results,
                "new_factors": new_factors,
                "alpha158_factors": alpha158_factors,
                "total_new_factors": len(new_factors),
                "research_method": "rd_agent_real"
            }

        except Exception as e:
            logger.error(f"RD-Agent因子研究失败: {e}")
            raise RuntimeError(f"RD-Agent因子研究失败: {e}")

    async def get_learning_monitoring_data(self) -> Dict[str, Any]:
        """获取学习监控数据"""
        try:
            if not self.current_session:
                return {
                    "success": False,
                    "error": "无活跃学习会话"
                }

            session = self.current_session
            session_id = session["session_id"]

            # 计算学习进度
            start_time = datetime.fromisoformat(session["start_time"])
            current_time = datetime.now()
            elapsed_time = (current_time - start_time).total_seconds() / 60  # 分钟

            # 获取会话结果
            results = session.get("results", {})

            # 计算完成的阶段数
            completed_phases = 0
            total_phases = 8  # 学习流程总共8个阶段

            phase_status = {
                "stock_selection": bool(results.get("selected_stocks")),
                "data_collection": bool(results.get("data_collection")),
                "market_info": bool(results.get("market_info")),
                "risk_analysis": bool(results.get("risk_analysis")),
                "technical_analysis": bool(results.get("technical_analysis")),
                "strategy_testing": bool(results.get("strategy_testing")),
                "four_stars_debate": bool(results.get("four_stars_debate")),
                "trading_execution": bool(results.get("trading_execution"))
            }

            completed_phases = sum(1 for completed in phase_status.values() if completed)
            learning_progress = completed_phases / total_phases

            # 计算绩效评分
            performance_score = 0.0
            if results.get("trading_execution"):
                trading_stats = results["trading_execution"].get("trading_statistics", {})
                total_profit = trading_stats.get("total_profit_loss", 0)
                if total_profit > 0:
                    performance_score = min(1.0, total_profit / 10000)  # 基于盈利计算评分
                else:
                    performance_score = max(0.0, 0.5 + total_profit / 20000)  # 亏损时的评分

            # 监控指标
            metrics = {
                "learning_progress": learning_progress,
                "completed_phases": completed_phases,
                "total_phases": total_phases,
                "elapsed_time_minutes": elapsed_time,
                "performance_score": performance_score,
                "phase_status": phase_status,
                "session_health": "healthy" if completed_phases >= 3 else "warning"
            }

            return {
                "success": True,
                "session_id": session_id,
                "metrics": metrics,
                "monitoring_time": current_time.isoformat()
            }

        except Exception as e:
            logger.error(f"获取学习监控数据失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_learning_results(self) -> Dict[str, Any]:
        """获取学习结果"""
        try:
            if not self.current_session:
                return {
                    "success": False,
                    "error": "无活跃学习会话"
                }

            session = self.current_session
            results = session.get("results", {})

            # 统计学习成果
            achievements = {}

            # 因子开发成果
            if results.get("technical_analysis"):
                tech_results = results["technical_analysis"].get("technical_analysis_results", {})
                achievements["factors_developed"] = len(tech_results)
            else:
                achievements["factors_developed"] = 0

            # 策略创建成果
            if results.get("strategy_testing"):
                strategy_results = results["strategy_testing"].get("tested_strategies", {})
                achievements["strategies_created"] = len(strategy_results)
            else:
                achievements["strategies_created"] = 0

            # 模型训练成果
            if results.get("four_stars_debate"):
                achievements["models_trained"] = 1  # 四星辩论产生的综合模型
            else:
                achievements["models_trained"] = 0

            # 交易执行成果
            if results.get("trading_execution"):
                trading_stats = results["trading_execution"].get("trading_statistics", {})
                achievements["trades_executed"] = trading_stats.get("total_trades", 0)
                achievements["profit_loss"] = trading_stats.get("total_profit_loss", 0)
            else:
                achievements["trades_executed"] = 0
                achievements["profit_loss"] = 0

            # 学习洞察
            insights_count = 0
            if results.get("market_info"):
                insights_count += len(results["market_info"].get("market_info_results", {}))
            if results.get("risk_analysis"):
                insights_count += len(results["risk_analysis"].get("risk_analysis_results", {}))

            achievements["total_insights"] = insights_count
            achievements["skills_acquired"] = min(5, len([k for k, v in achievements.items() if v > 0]))

            return {
                "success": True,
                "session_id": session["session_id"],
                "achievements": achievements,
                "session_status": session.get("status", "unknown"),
                "results_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取学习结果失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_session_report(self) -> Dict[str, Any]:
        """获取会话报告"""
        try:
            if not self.current_session:
                return {
                    "success": False,
                    "error": "无活跃学习会话"
                }

            session = self.current_session
            session_id = session["session_id"]

            # 计算会话统计
            start_time = datetime.fromisoformat(session["start_time"])
            current_time = datetime.now()
            duration_minutes = (current_time - start_time).total_seconds() / 60

            results = session.get("results", {})

            # 统计完成的阶段
            phases_completed = 0
            phase_details = {}

            phase_mapping = {
                "selected_stocks": "股票选择",
                "data_collection": "数据收集",
                "market_info": "市场信息收集",
                "risk_analysis": "风险分析",
                "technical_analysis": "技术分析",
                "strategy_testing": "策略测试",
                "four_stars_debate": "四星辩论",
                "trading_execution": "交易执行"
            }

            for key, name in phase_mapping.items():
                if results.get(key):
                    phases_completed += 1
                    phase_details[name] = "已完成"
                else:
                    phase_details[name] = "未完成"

            # 会话统计
            session_stats = {
                "session_id": session_id,
                "duration_minutes": duration_minutes,
                "phases_completed": phases_completed,
                "total_phases": len(phase_mapping),
                "completion_rate": phases_completed / len(phase_mapping),
                "phase_details": phase_details
            }

            # 学习成果
            learning_outcomes = {}

            # 获取交易结果
            if results.get("trading_execution"):
                trading_stats = results["trading_execution"].get("trading_statistics", {})
                learning_outcomes.update({
                    "total_trades": trading_stats.get("total_trades", 0),
                    "profit_loss": trading_stats.get("total_profit_loss", 0),
                    "win_rate": trading_stats.get("win_rate", 0)
                })

            # 获取分析结果
            total_insights = 0
            if results.get("market_info"):
                total_insights += len(results["market_info"].get("market_info_results", {}))
            if results.get("risk_analysis"):
                total_insights += len(results["risk_analysis"].get("risk_analysis_results", {}))
            if results.get("technical_analysis"):
                total_insights += len(results["technical_analysis"].get("technical_analysis_results", {}))

            learning_outcomes.update({
                "total_insights": total_insights,
                "skills_acquired": min(5, phases_completed),
                "knowledge_points": phases_completed * 3  # 每个阶段3个知识点
            })

            # 绩效评估
            performance_score = 0.0
            if learning_outcomes.get("profit_loss", 0) > 0:
                performance_score = min(10.0, learning_outcomes["profit_loss"] / 1000)
            else:
                performance_score = max(0.0, 5.0 + learning_outcomes.get("profit_loss", 0) / 2000)

            performance_evaluation = {
                "overall_score": performance_score,
                "learning_efficiency": phases_completed / max(1, duration_minutes / 10),  # 每10分钟完成阶段数
                "trading_performance": learning_outcomes.get("win_rate", 0),
                "analysis_depth": total_insights / max(1, len(results.get("selected_stocks", []))),
                "grade": "优秀" if performance_score >= 8 else "良好" if performance_score >= 6 else "及格" if performance_score >= 4 else "需改进"
            }

            # 生成报告数据
            report_data = {
                "session_stats": session_stats,
                "learning_outcomes": learning_outcomes,
                "performance_evaluation": performance_evaluation,
                "detailed_results": results,
                "report_generation_time": current_time.isoformat()
            }

            return {
                "success": True,
                "session_id": session_id,
                "report_data": report_data,
                "report_time": current_time.isoformat()
            }

        except Exception as e:
            logger.error(f"获取会话报告失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }



# 全局统一系统实例
unified_yaoguang_system = UnifiedYaoguangSystem()
