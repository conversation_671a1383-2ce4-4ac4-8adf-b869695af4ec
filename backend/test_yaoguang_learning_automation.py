#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星学习模式自动化全流程测试
测试瑶光星学习自动化的8个阶段完整流程
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any, List
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class YaoguangLearningAutomationTest:
    """瑶光星学习模式自动化全流程测试"""
    
    def __init__(self):
        self.test_results = {
            "stage_1_initiation": {},
            "stage_2_practice": {},
            "stage_3_research_reflection": {},
            "stage_4_factor_development": {},
            "stage_5_model_training": {},
            "stage_6_strategy_generation": {},
            "stage_7_backtesting": {},
            "stage_8_skill_library_upload": {},
            "overall_workflow": {}
        }
        
    async def run_complete_learning_test(self):
        """运行完整的学习自动化测试"""
        print("🌟 开始瑶光星学习模式自动化全流程测试")
        print("=" * 80)
        
        try:
            # 导入瑶光星学习自动化系统
            from roles.yaoguang_star.automation.quantitative_research_automation import quantitative_research_automation
            
            print("✅ 瑶光星学习自动化系统导入成功")
            
            # 测试8个学习阶段
            await self.test_stage_1_initiation(quantitative_research_automation)
            await self.test_stage_2_practice(quantitative_research_automation)
            await self.test_stage_3_research_reflection(quantitative_research_automation)
            await self.test_stage_4_factor_development(quantitative_research_automation)
            await self.test_stage_5_model_training(quantitative_research_automation)
            await self.test_stage_6_strategy_generation(quantitative_research_automation)
            await self.test_stage_7_backtesting(quantitative_research_automation)
            await self.test_stage_8_skill_library_upload(quantitative_research_automation)
            
            # 测试完整工作流
            await self.test_complete_workflow(quantitative_research_automation)
            
            # 生成测试报告
            await self.generate_learning_test_report()
            
        except Exception as e:
            print(f"❌ 瑶光星学习自动化测试失败: {e}")
            self.test_results["overall_workflow"]["error"] = str(e)
    
    async def test_stage_1_initiation(self, automation_system):
        """测试阶段1：启动阶段"""
        print("\n🚀 测试阶段1：学习启动阶段")
        print("-" * 50)
        
        try:
            # 测试学习模式启动
            result = await automation_system.start_automation(mode="learning")
            
            if result.get("success"):
                print("  ✅ 学习模式启动成功")
                self.test_results["stage_1_initiation"]["startup"] = True
                
                # 检查学习配置
                if "learning_config" in result:
                    print("  ✅ 学习配置加载成功")
                    self.test_results["stage_1_initiation"]["config"] = True
                else:
                    print("  ⚠️ 学习配置缺失")
                    self.test_results["stage_1_initiation"]["config"] = False
                    
            else:
                print(f"  ❌ 学习模式启动失败: {result.get('error')}")
                self.test_results["stage_1_initiation"]["startup"] = False
                
        except Exception as e:
            print(f"  ❌ 阶段1测试异常: {e}")
            self.test_results["stage_1_initiation"]["error"] = str(e)
    
    async def test_stage_2_practice(self, automation_system):
        """测试阶段2：多角色协作练习"""
        print("\n🤝 测试阶段2：多角色协作练习")
        print("-" * 50)
        
        try:
            # 测试开阳星选股
            print("  🔍 测试开阳星选股...")
            from roles.kaiyang_star.services.kaiyang_automation_system import kaiyang_automation_system
            
            kaiyang_result = await kaiyang_automation_system.execute_stock_selection_automation()
            
            if kaiyang_result.get("success"):
                print("  ✅ 开阳星选股成功")
                self.test_results["stage_2_practice"]["kaiyang_selection"] = True
                
                # 获取选中的股票
                selected_stocks = kaiyang_result.get("automation_result", {}).get("selected_stocks", [])
                if selected_stocks:
                    test_stock = selected_stocks[0] if isinstance(selected_stocks[0], str) else selected_stocks[0].get("stock_code")
                    print(f"  📊 选中测试股票: {test_stock}")
                    
                    # 测试瑶光星练习分析
                    practice_result = await automation_system.execute_practice_analysis(test_stock)
                    
                    if practice_result.get("success"):
                        print("  ✅ 瑶光星练习分析成功")
                        self.test_results["stage_2_practice"]["practice_analysis"] = True
                    else:
                        print(f"  ❌ 瑶光星练习分析失败: {practice_result.get('error')}")
                        self.test_results["stage_2_practice"]["practice_analysis"] = False
                else:
                    print("  ⚠️ 未获取到选中股票")
                    self.test_results["stage_2_practice"]["practice_analysis"] = False
            else:
                print(f"  ❌ 开阳星选股失败: {kaiyang_result.get('error')}")
                self.test_results["stage_2_practice"]["kaiyang_selection"] = False
                
        except Exception as e:
            print(f"  ❌ 阶段2测试异常: {e}")
            self.test_results["stage_2_practice"]["error"] = str(e)
    
    async def test_stage_3_research_reflection(self, automation_system):
        """测试阶段3：研究反思"""
        print("\n🔬 测试阶段3：研究反思")
        print("-" * 50)
        
        try:
            # 测试研究模式反思
            reflection_result = await automation_system.execute_research_reflection()
            
            if reflection_result.get("success"):
                print("  ✅ 研究反思执行成功")
                self.test_results["stage_3_research_reflection"]["execution"] = True
                
                # 检查反思内容
                reflection_data = reflection_result.get("reflection_data", {})
                if reflection_data:
                    print(f"  📝 反思要点数量: {len(reflection_data)}")
                    self.test_results["stage_3_research_reflection"]["content"] = True
                else:
                    print("  ⚠️ 反思内容为空")
                    self.test_results["stage_3_research_reflection"]["content"] = False
                    
            else:
                print(f"  ❌ 研究反思失败: {reflection_result.get('error')}")
                self.test_results["stage_3_research_reflection"]["execution"] = False
                
        except Exception as e:
            print(f"  ❌ 阶段3测试异常: {e}")
            self.test_results["stage_3_research_reflection"]["error"] = str(e)
    
    async def test_stage_4_factor_development(self, automation_system):
        """测试阶段4：因子开发"""
        print("\n🧮 测试阶段4：因子开发")
        print("-" * 50)
        
        try:
            # 测试因子开发
            factor_result = await automation_system.execute_factor_development()
            
            if factor_result.get("success"):
                print("  ✅ 因子开发执行成功")
                self.test_results["stage_4_factor_development"]["execution"] = True
                
                # 检查开发的因子
                factors = factor_result.get("developed_factors", [])
                if factors:
                    print(f"  🔢 开发因子数量: {len(factors)}")
                    self.test_results["stage_4_factor_development"]["factors_count"] = len(factors)
                else:
                    print("  ⚠️ 未开发出新因子")
                    self.test_results["stage_4_factor_development"]["factors_count"] = 0
                    
            else:
                print(f"  ❌ 因子开发失败: {factor_result.get('error')}")
                self.test_results["stage_4_factor_development"]["execution"] = False
                
        except Exception as e:
            print(f"  ❌ 阶段4测试异常: {e}")
            self.test_results["stage_4_factor_development"]["error"] = str(e)
    
    async def test_stage_5_model_training(self, automation_system):
        """测试阶段5：模型训练"""
        print("\n🤖 测试阶段5：模型训练")
        print("-" * 50)
        
        try:
            # 测试模型训练
            training_result = await automation_system.execute_model_training()
            
            if training_result.get("success"):
                print("  ✅ 模型训练执行成功")
                self.test_results["stage_5_model_training"]["execution"] = True
                
                # 检查训练结果
                model_performance = training_result.get("model_performance", {})
                if model_performance:
                    accuracy = model_performance.get("accuracy", 0)
                    print(f"  📊 模型准确率: {accuracy:.2%}")
                    self.test_results["stage_5_model_training"]["accuracy"] = accuracy
                else:
                    print("  ⚠️ 模型性能数据缺失")
                    self.test_results["stage_5_model_training"]["accuracy"] = 0
                    
            else:
                print(f"  ❌ 模型训练失败: {training_result.get('error')}")
                self.test_results["stage_5_model_training"]["execution"] = False
                
        except Exception as e:
            print(f"  ❌ 阶段5测试异常: {e}")
            self.test_results["stage_5_model_training"]["error"] = str(e)
    
    async def test_stage_6_strategy_generation(self, automation_system):
        """测试阶段6：策略生成"""
        print("\n📈 测试阶段6：策略生成")
        print("-" * 50)
        
        try:
            # 测试策略生成
            strategy_result = await automation_system.execute_strategy_generation()
            
            if strategy_result.get("success"):
                print("  ✅ 策略生成执行成功")
                self.test_results["stage_6_strategy_generation"]["execution"] = True
                
                # 检查生成的策略
                strategies = strategy_result.get("generated_strategies", [])
                if strategies:
                    print(f"  📋 生成策略数量: {len(strategies)}")
                    self.test_results["stage_6_strategy_generation"]["strategies_count"] = len(strategies)
                else:
                    print("  ⚠️ 未生成新策略")
                    self.test_results["stage_6_strategy_generation"]["strategies_count"] = 0
                    
            else:
                print(f"  ❌ 策略生成失败: {strategy_result.get('error')}")
                self.test_results["stage_6_strategy_generation"]["execution"] = False
                
        except Exception as e:
            print(f"  ❌ 阶段6测试异常: {e}")
            self.test_results["stage_6_strategy_generation"]["error"] = str(e)
    
    async def test_stage_7_backtesting(self, automation_system):
        """测试阶段7：回测验证"""
        print("\n📊 测试阶段7：回测验证")
        print("-" * 50)
        
        try:
            # 测试回测验证
            backtest_result = await automation_system.execute_backtesting()
            
            if backtest_result.get("success"):
                print("  ✅ 回测验证执行成功")
                self.test_results["stage_7_backtesting"]["execution"] = True
                
                # 检查回测结果
                backtest_data = backtest_result.get("backtest_results", {})
                if backtest_data:
                    total_return = backtest_data.get("total_return", 0)
                    sharpe_ratio = backtest_data.get("sharpe_ratio", 0)
                    print(f"  💰 总收益率: {total_return:.2%}")
                    print(f"  📈 夏普比率: {sharpe_ratio:.2f}")
                    self.test_results["stage_7_backtesting"]["total_return"] = total_return
                    self.test_results["stage_7_backtesting"]["sharpe_ratio"] = sharpe_ratio
                else:
                    print("  ⚠️ 回测结果数据缺失")
                    self.test_results["stage_7_backtesting"]["total_return"] = 0
                    self.test_results["stage_7_backtesting"]["sharpe_ratio"] = 0
                    
            else:
                print(f"  ❌ 回测验证失败: {backtest_result.get('error')}")
                self.test_results["stage_7_backtesting"]["execution"] = False
                
        except Exception as e:
            print(f"  ❌ 阶段7测试异常: {e}")
            self.test_results["stage_7_backtesting"]["error"] = str(e)
    
    async def test_stage_8_skill_library_upload(self, automation_system):
        """测试阶段8：技能库上传"""
        print("\n📚 测试阶段8：技能库上传")
        print("-" * 50)
        
        try:
            # 测试技能库上传
            upload_result = await automation_system.execute_skill_library_upload()
            
            if upload_result.get("success"):
                print("  ✅ 技能库上传执行成功")
                self.test_results["stage_8_skill_library_upload"]["execution"] = True
                
                # 检查上传的技能
                uploaded_skills = upload_result.get("uploaded_skills", [])
                if uploaded_skills:
                    print(f"  🎯 上传技能数量: {len(uploaded_skills)}")
                    self.test_results["stage_8_skill_library_upload"]["skills_count"] = len(uploaded_skills)
                else:
                    print("  ⚠️ 未上传新技能")
                    self.test_results["stage_8_skill_library_upload"]["skills_count"] = 0
                    
            else:
                print(f"  ❌ 技能库上传失败: {upload_result.get('error')}")
                self.test_results["stage_8_skill_library_upload"]["execution"] = False
                
        except Exception as e:
            print(f"  ❌ 阶段8测试异常: {e}")
            self.test_results["stage_8_skill_library_upload"]["error"] = str(e)
    
    async def test_complete_workflow(self, automation_system):
        """测试完整工作流"""
        print("\n🔄 测试完整学习工作流")
        print("-" * 50)
        
        try:
            # 测试完整的学习工作流
            workflow_result = await automation_system.execute_complete_learning_workflow()
            
            if workflow_result.get("success"):
                print("  ✅ 完整学习工作流执行成功")
                self.test_results["overall_workflow"]["execution"] = True
                
                # 检查工作流统计
                workflow_stats = workflow_result.get("workflow_stats", {})
                if workflow_stats:
                    completed_stages = workflow_stats.get("completed_stages", 0)
                    total_time = workflow_stats.get("total_time_minutes", 0)
                    print(f"  📊 完成阶段数: {completed_stages}/8")
                    print(f"  ⏱️ 总耗时: {total_time:.1f}分钟")
                    self.test_results["overall_workflow"]["completed_stages"] = completed_stages
                    self.test_results["overall_workflow"]["total_time"] = total_time
                else:
                    print("  ⚠️ 工作流统计数据缺失")
                    self.test_results["overall_workflow"]["completed_stages"] = 0
                    self.test_results["overall_workflow"]["total_time"] = 0
                    
            else:
                print(f"  ❌ 完整学习工作流失败: {workflow_result.get('error')}")
                self.test_results["overall_workflow"]["execution"] = False
                
        except Exception as e:
            print(f"  ❌ 完整工作流测试异常: {e}")
            self.test_results["overall_workflow"]["error"] = str(e)
    
    async def generate_learning_test_report(self):
        """生成学习测试报告"""
        print("\n📋 生成瑶光星学习自动化测试报告")
        print("=" * 80)
        
        # 计算总体成功率
        total_tests = 0
        passed_tests = 0
        
        for stage, results in self.test_results.items():
            for test_name, result in results.items():
                if isinstance(result, bool):
                    total_tests += 1
                    if result:
                        passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"📊 学习自动化测试统计:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过测试: {passed_tests}")
        print(f"  成功率: {success_rate:.1f}%")
        
        # 评估学习自动化质量
        if success_rate >= 90:
            print("🎉 瑶光星学习自动化质量优秀!")
        elif success_rate >= 75:
            print("✅ 瑶光星学习自动化质量良好!")
        elif success_rate >= 60:
            print("⚠️ 瑶光星学习自动化质量一般，需要优化")
        else:
            print("❌ 瑶光星学习自动化质量不佳，需要重新设计")
        
        # 保存详细报告
        report = {
            "test_results": self.test_results,
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "success_rate": success_rate,
                "test_time": datetime.now().isoformat()
            }
        }
        
        report_filename = f"yaoguang_learning_automation_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细报告已保存到: {report_filename}")

async def main():
    """主函数"""
    test_runner = YaoguangLearningAutomationTest()
    await test_runner.run_complete_learning_test()

if __name__ == "__main__":
    asyncio.run(main())
